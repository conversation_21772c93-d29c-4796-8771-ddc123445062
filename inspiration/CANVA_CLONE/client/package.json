{"name": "client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fabric": "^6.6.2", "file-saver": "^2.0.5", "jspdf": "^3.0.1", "lodash": "^4.17.21", "lucide-react": "^0.486.0", "next": "15.2.4", "next-auth": "^5.0.0-beta.25", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@tailwindcss/postcss": "^4", "tailwindcss": "^4"}}
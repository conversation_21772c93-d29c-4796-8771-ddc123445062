@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Header gradient background */
.header-gradient {
  background: linear-gradient(90deg, #0099ff 0%, #4c6fff 100%);
}

/* Sidebar styles */
.sidebar {
  width: 64px;
  background-color: #f5f5f5;
  border-right: 1px solid #e6e6e6;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10;
}

.sidebar-item {
  width: 100%;
  height: 72px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px;
  position: relative;
  color: #303030;
  cursor: pointer;
}

.sidebar-item:hover {
  background-color: #e6e6e6;
}

.sidebar-item.active {
  background-color: #e6e6e6;
}

.sidebar-item-icon {
  margin-bottom: 4px;
}

.sidebar-item-label {
  font-size: 12px;
  font-weight: 500;
}

/* Secondary panel */
.secondary-panel {
  height: 100%;
  background-color: white;
  border-right: 1px solid #e6e6e6;
  position: relative;
  transition: width 0.3s ease, opacity 0.3s ease;
  z-index: 5;
}

.secondary-panel.collapsed {
  width: 0;
  opacity: 0;
  overflow: hidden;
}

/* Panel header */
.panel-header {
  padding: 16px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e6e6e6;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  margin-right: 8px;
  color: #303030;
}

.panel-title {
  font-size: 16px;
  font-weight: 500;
  color: #303030;
}

/* Search container */
.search-container {
  padding: 12px 16px;
  border-bottom: 1px solid #e6e6e6;
}

.search-input-wrapper {
  position: relative;
  width: 100%;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: #757575;
}

.search-input {
  width: 100%;
  height: 36px;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  padding: 0 12px 0 36px;
  font-size: 14px;
  background-color: #f9f9f9;
}

.search-input:focus {
  outline: none;
  border-color: #0d99ff;
}

/* Panel content */
.panel-content {
  height: calc(100% - 110px); /* Adjust based on header + search height */
  overflow-y: auto;
}

/* Collapse button */
.collapse-button {
  position: absolute;
  right: -12px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background-color: white;
  border: 1px solid #e6e6e6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 20;
}

.collapse-button:hover {
  background-color: #f5f5f5;
}

/* Hover panel - keeping for compatibility with other files */
.hover-panel {
  position: fixed;
  left: 64px;
  top: 56px; /* Height of the header */
  bottom: 48px; /* Height of the bottom bar */
  width: 280px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e6e6e6;
  opacity: 0;
  transform: translateX(-10px);
  pointer-events: none;
  transition: opacity 0.2s ease, transform 0.2s ease;
  z-index: 100;
  overflow-y: auto; /* Allow scrolling if content is too tall */
}

/* Show panel on hover */
.sidebar-item:hover .hover-panel:not(.force-closed) {
  opacity: 1;
  transform: translateX(0);
  pointer-events: auto;
}

/* Keep panel visible when pinned */
.hover-panel.pinned {
  opacity: 1;
  transform: translateX(0);
  pointer-events: auto;
}

/* Force hide panel when closed */
.hover-panel.force-closed {
  opacity: 0 !important;
  transform: translateX(-10px) !important;
  pointer-events: none !important;
}

/* Header styles */
.header {
  height: 56px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  color: white;
}

.header-button {
  height: 36px;
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s;
  color: white;
  background-color: transparent;
  border: none;
  cursor: pointer;
}

.header-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.header-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.header-title-input {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 4px;
  height: 36px;
  padding: 0 12px;
  text-align: center;
  width: 100%;
  max-width: 400px;
}

.header-title-input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  background-color: rgba(255, 255, 255, 0.25);
}

/* Bottom bar styles */
.bottom-bar {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-top: 1px solid #e6e6e6;
  background-color: #f5f5f5;
}

/* Pro badge */
.pro-badge {
  color: #ffca28;
  margin-right: 4px;
}

/* Shape item in elements panel - Updated to match Canva's clean design */
.shape-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s;
}

.shape-item:hover {
  transform: translateY(-2px);
}

.shape-item canvas {
  margin-bottom: 4px;
}

.upgrade-button {
  font-weight: 500;
  transition: all 0.2s ease;
}

.upgrade-button:hover {
  transform: translateY(-1px);
}

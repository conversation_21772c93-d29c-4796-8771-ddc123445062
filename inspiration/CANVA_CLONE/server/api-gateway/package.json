{"name": "api-gateway", "version": "1.0.0", "description": "", "main": "src/server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node src/server.js", "dev": "nodemon src/server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "express-http-proxy": "^2.1.1", "google-auth-library": "^9.15.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "nodemon": "^3.1.9"}}
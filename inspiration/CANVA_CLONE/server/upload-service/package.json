{"name": "upload-service", "version": "1.0.0", "description": "", "main": "src/server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node src/server.js", "dev": "nodemon src/server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.8.4", "cloudinary": "^2.6.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.1", "multer": "^1.4.5-lts.2", "nodemon": "^3.1.9"}}
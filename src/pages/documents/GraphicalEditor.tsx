import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import MermaidGraphicalEditor from '../../components/editor/MermaidGraphicalEditor';

// Standalone Mermaid Graphical Editor
const GraphicalEditor: React.FC = () => {
  const navigate = useNavigate();

  // Function to navigate back to dashboard or document generator
  const handleBack = () => {
    // Check if we came from the document generator page
    try {
      const referrer = document.referrer;
      if (referrer && referrer.includes('/app/documents')) {
        // If we came from documents, go back there
        window.history.back();
      } else {
        // Otherwise go to the main dashboard
        navigate('/app');
      }
    } catch (e) {
      // If any error, default to main dashboard
      navigate('/app');
    }
  };

  useEffect(() => {
    document.title = 'AuditReady Mermaid Editor';
  }, []);

  return (
    <MermaidGraphicalEditor
      designId="standalone-mermaid-editor"
      showBackButton={true}
      onBack={handleBack}
    />
  );
};

export default GraphicalEditor;
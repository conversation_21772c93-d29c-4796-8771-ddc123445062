/* Timeline Template Specific Styles */

/* Timeline positioning container */
.timeline-container {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Timeline title styling */
.yearly-timeline-element[id="timeline-title"] {
  font-family: 'Inter', sans-serif !important;
  letter-spacing: 1px !important;
  text-align: center !important;
}

.yearly-timeline-element[id="timeline-subtitle"] {
  font-family: 'Inter', sans-serif !important;
  letter-spacing: 0.5px !important;
  text-align: center !important;
  font-weight: 400 !important;
}

/* Timeline bar styling */
.yearly-timeline-element[id="timeline-bar-bg"] {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* Month segments */
.yearly-timeline-element[id^="timeline-bar-"] {
  border-right: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* Month labels */
.yearly-timeline-element[id^="timeline-label-"] {
  font-family: 'Inter', sans-serif !important;
  font-weight: 700 !important;
  letter-spacing: 0.5px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* Diamond styling */
.yearly-timeline-element[id^="month-"][id$="-diamond"] {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15) !important;
  z-index: 2 !important;
}

/* Month number label styling */
.yearly-timeline-element[id^="month-"][id$="-diamond"] div {
  transform: rotate(-45deg) !important;
  font-weight: 700 !important;
  letter-spacing: 0.5px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* Month header styling */
.yearly-timeline-element[id^="month-"][id$="-label"] {
  font-weight: 700 !important;
  letter-spacing: 0.5px !important;
}

/* Title styling */
.yearly-timeline-element[id^="month-"][id$="-title"] {
  font-family: 'Inter', sans-serif !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px !important;
  text-transform: uppercase !important;
  opacity: 0.8 !important;
}

/* Month segment styling */
[id^="month-segment-"] {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-weight: 600;
}

/* Fit the view to use entire container */
.react-flow__viewport {
  /* display: flex !important; */ /* Commented out aggressive centering */
  /* justify-content: center !important; */ /* Commented out aggressive centering */
  /* align-items: center !important; */ /* Commented out aggressive centering */
  width: 100% !important; /* Retaining size definition */
  height: 100% !important; /* Retaining size definition */
}

/* Remove gray backgrounds but keep proper positioning */
.locked-template-element[id="timeline-container"] {
  position: absolute !important;
  z-index: -1 !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

/* Modify the timeline container to use full width */
.yearly-timeline-element {
  z-index: 5 !important;
}

/* Ensure template content stays within the visible container */
.react-flow__renderer {
  overflow: visible !important;
}

/* Remove gray background for templates but maintain positioning */
.locked-template-element {
  position: relative;
  z-index: 1;
  box-shadow: none !important;
}

/* Timeline container styles */
.yearly-timeline-element {
  position: relative;
  z-index: 1;
}

/* Make sure editor wrapper shows all content */
.react-flow-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

/* Remove background containers but maintain positioning */
.react-flow__node[data-type="shapeNode"][id="timeline-container-bg"],
.react-flow__node[data-type="shapeNode"][id="process-flow-container"] {
  z-index: 0;
  box-shadow: none !important;
}

/* Ensure interactive elements are above the background */
.react-flow__node[data-type="textNode"],
.react-flow__node[data-type="shapeNode"]:not([id="timeline-container-bg"]):not([id="process-flow-container"]) {
  z-index: 2;
}

/* Ensure elements stay within bounds */
.react-flow {
  width: 100% !important;
  height: 100% !important;
}

/* Ensure nodes have proper z-index layering */
.react-flow__node {
  z-index: 5;
}

/* Improve visibility of edges in all templates */
.react-flow__edge {
  z-index: 3;
}

.react-flow__edge-path {
  stroke-width: 2px !important;
}

/* Process flow specific styling */
.react-flow__node[id^="step"] {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Ensure hexagons render correctly */
.react-flow__node-shapeNode[data-shapetype="hexagon"] {
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%) !important;
}

/* Customize scrollbar appearance */
.react-flow__pane {
  background: #ffffff !important;
  overflow: hidden !important;
}

.react-flow__pane::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.react-flow__pane::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.react-flow__pane::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

.react-flow__pane::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Apply drop shadow to all timeline elements */
[id^="hexagon-"] {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15)) !important;
  transition: all 0.3s ease !important;
}

[id^="hexagon-"]:hover {
  filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.25)) !important;
  transform: translateY(-5px) !important;
  z-index: 10 !important;
}

/* Style month segments bar for a clean look */
#month-bar {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1) !important;
  border-radius: 8px !important;
}

[id^="month-segment-"] {
  transition: all 0.3s ease !important;
  position: relative !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
}

[id^="month-segment-"]:hover {
  transform: translateY(-2px) !important;
  z-index: 5 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

/* Enhance title text with better spacing */
[id^="title-"] {
  font-family: 'Inter', 'Roboto', sans-serif !important;
  letter-spacing: 0.5px !important;
  font-weight: 600 !important;
  color: #333 !important;
  margin-bottom: 10px !important;
}

/* Content text styling */
[id^="content-"] {
  font-family: 'Inter', 'Roboto', sans-serif !important;
  line-height: 1.5 !important;
  color: #555 !important;
  transition: all 0.3s ease !important;
  padding: 5px !important;
  border-radius: 4px !important;
}

[id^="content-"]:hover {
  background-color: rgba(255, 255, 255, 0.8) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* Title styling */
#timeline-title {
  font-family: 'Inter', 'Roboto', sans-serif !important;
  letter-spacing: 2px !important;
  text-transform: uppercase !important;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
}

#timeline-subtitle {
  font-family: 'Inter', 'Roboto', sans-serif !important;
  letter-spacing: 1px !important;
  color: #666666 !important;
  opacity: 0.9 !important;
}

/* Ensure the entire timeline is centered in the view */
#yearly-timeline-container {
  position: relative;
  width: 100% !important;
  height: 100% !important;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff;
}

/* Better visibility for hexagon numbers */
[id^="hexagon-"] {
  font-weight: 700;
  font-size: 18px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Make sure month text is properly centered */
[id^="month-segment-"] {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-weight: 600;
}

/* Professional styling for shape nodes */
.react-flow__node-shapeNode {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: all 0.2s ease-in-out;
}

.react-flow__node-shapeNode:hover {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
  transform: translateY(-2px);
}

/* Grid styling for professional appearance */
.react-flow__viewport {
  background-image: radial-gradient(#e1e1e1 1px, transparent 1px);
  background-size: 20px 20px;
  /* This rule is for background image, centering is handled above or by default */
}

/* Nice styling for labels */
.react-flow__node-textNode {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  color: #333333;
}

/* Enhanced drop shadows for hexagons */
[id^="hexagon-"] {
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.15));
  transition: transform 0.3s, filter 0.3s;
}

[id^="hexagon-"]:hover {
  filter: drop-shadow(0 6px 10px rgba(0, 0, 0, 0.25));
  transform: translateY(-3px);
  z-index: 10;
}

/* Professional handling of month segments */
[id^="month-segment-"] {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

[id^="month-segment-"]:hover {
  filter: brightness(110%);
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

/* Ensure arrows are visible with appropriate styling */
.react-flow__edge-path {
  stroke-width: 2px !important;
}

/* Better visual distinction for arrow markers */
.react-flow__arrowhead {
  fill: currentColor;
  stroke: none;
}

/* Improve the flow container appearance */
.react-flow {
  width: 100% !important;
  height: 100% !important;
  background-color: #fafafa;
}

/* Fix any transform issues */
.react-flow__viewport {
  transform-origin: 0 0 !important;
  /* This rule is for transform-origin, centering is handled above or by default */
}

/* Ensure all template elements are properly contained */
.react-flow__renderer .react-flow__nodes,
.react-flow__renderer .react-flow__edges {
  pointer-events: all;
}

/* Enhanced visual effects for text elements */
.react-flow__node-textNode {
  filter: none;
  transition: filter 0.2s, transform 0.2s;
}

.react-flow__node-textNode:hover {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transform: translateY(-1px);
}

/* Ensure process flow boxes have consistent styling */
#start, #step1, #step2, #end {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

/* Style the controls for a cleaner look */
.react-flow__controls {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.react-flow__controls-button {
  border: none !important;
  background: white !important;
  color: #555 !important;
  transition: background 0.2s, color 0.2s;
}

.react-flow__controls-button:hover {
  background: #f5f5f5 !important;
  color: #000 !important;
}

/* Better styling for node selection */
.react-flow__node.selected {
  outline: 2px solid #2065D1 !important;
}

/* Use proper cursor styling */
.react-flow__node {
  cursor: pointer;
}

/* Fix for org chart styling */
#ceo, #cto, #cfo, #coo {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

#ceo:hover, #cto:hover, #cfo:hover, #coo:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

/* Make sure timeline elements are properly sized */
.yearly-timeline-element {
  box-sizing: border-box;
} 
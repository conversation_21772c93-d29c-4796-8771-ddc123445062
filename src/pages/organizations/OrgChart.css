.org-chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 700px;
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  overflow: hidden;
  background-color: var(--background);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  animation: fadeIn 0.3s ease-out;
  --card-background: hsl(0, 0%, 100%);
  --card-header-background: hsl(210, 20%, 98%);
  --input-background: hsl(0, 0%, 100%);
  --primary: hsl(217, 91%, 60%);
  --primary-light: hsl(214, 100%, 97%);
  --muted-foreground: hsl(215, 16%, 47%);
  --foreground: hsl(217, 19%, 27%);
  --border: hsla(220, 13%, 91%, 1);
}

.org-chart {
  width: 100%;
  height: 100%;
  flex: 1;
  background-color: transparent;
  transition: all 0.3s ease;
  position: relative;
}

.fullscreen-chart {
  background-color: var(--background);
  padding: 20px;
}

.org-chart-toolbar {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  padding: 14px 18px;
  background-color: var(--card-background);
  border-bottom: 1px solid var(--border);
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  animation: slideIn 0.3s ease-out;
}

.org-chart-toolbar-section {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 8px;
}

.org-chart-toolbar-section:not(:last-child) {
  border-right: 1px solid var(--border);
  padding-right: 14px;
  margin-right: 6px;
}

.search-section {
  flex-grow: 1;
}

.org-chart-search {
  position: relative;
  width: 100%;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: var(--muted-foreground);
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding-left: 36px !important;
  height: 38px;
  border-radius: 8px;
  background-color: var(--input-background);
  border: 1px solid var(--border);
  color: var(--foreground);
  transition: all 0.15s ease;
  font-size: 14px;
}

.search-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.clear-search-button {
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  height: 24px;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 16px;
  font-weight: bold;
  color: var(--muted-foreground);
}

.org-chart-button {
  height: 38px;
  width: 38px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s ease;
}

.org-chart-button:hover {
  background-color: var(--primary-light);
  color: var(--primary);
}

.org-chart-status-bar {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 10px 18px;
  background-color: var(--card-background);
  border-top: 1px solid var(--border);
  font-size: 12px;
  color: var(--muted-foreground);
}

.org-chart-status-item {
  display: flex;
  align-items: center;
}

/* Node styling */
.org-node {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  border: 2px solid transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.org-node.highlighted {
  border-color: #fde047 !important;
  border-width: 3px;
  box-shadow: 0 0 0 2px rgba(253, 224, 71, 0.5), 0 4px 12px rgba(0, 0, 0, 0.2);
  animation: pulse 1.5s infinite;
}

.org-node-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.org-node-title {
  font-weight: 600;
  font-size: 15px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.org-node-subtitle {
  font-size: 13px;
  font-weight: 400;
  opacity: 0.9;
  overflow: hidden;
  text-overflow: ellipsis;
}

.org-node-detail {
  font-size: 12px;
  opacity: 0.8;
  margin-top: 2px;
  max-height: 32px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* D3 Org Chart customizations */
.node.node--leaf {
  cursor: pointer;
}

.link {
  stroke-width: 1.5px !important;
  stroke: #94a3b8 !important;
}

.highlighted + .link {
  stroke: #f97316 !important;
  stroke-width: 2px !important;
}

.node.hovered {
  filter: brightness(1.1);
}

.node-button-circle {
  fill: white !important;
  stroke: #2563eb !important;
}

.node-button-text {
  fill: #2563eb !important;
  font-weight: 600 !important;
  font-size: 12px !important;
}

/* Dark mode overrides */
.dark {
  --card-background: hsl(222, 20%, 12%);
  --card-header-background: hsl(222, 20%, 10%);
  --input-background: hsl(222, 20%, 15%);
  --primary: hsl(217, 91%, 60%);
  --primary-light: hsla(217, 91%, 60%, 0.15);
  --muted-foreground: hsl(215, 16%, 65%);
  --foreground: hsl(213, 31%, 91%);
  --border: hsla(222, 13%, 20%, 1);
}

.dark .org-chart-container {
  --card-background: hsl(222, 20%, 12%);
  --card-header-background: hsl(222, 20%, 10%);
  --input-background: hsl(222, 20%, 15%);
  --primary: hsl(217, 91%, 60%);
  --primary-light: hsla(217, 91%, 60%, 0.15);
  --muted-foreground: hsl(215, 16%, 65%);
  --foreground: hsl(213, 31%, 91%);
  --border: hsla(222, 13%, 20%, 1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.dark .link {
  stroke: #475569 !important;
}

.dark .node-button-circle {
  fill: #1e293b !important;
}

.dark .org-node.dark {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

/* Responsive fixes */
@media (max-width: 768px) {
  .org-chart-toolbar {
    flex-direction: column;
    align-items: flex-start;
    padding: 12px;
  }
  
  .org-chart-toolbar-section {
    width: 100%;
    justify-content: space-between;
    padding: 8px 0;
    border-right: none;
    margin-right: 0;
    border-bottom: 1px solid var(--border);
  }
  
  .org-chart-toolbar-section:last-child {
    border-bottom: none;
  }
  
  .search-section {
    order: -1;
  }
  
  .org-chart-search {
    max-width: 100%;
  }
}

/* Animation effects */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(253, 224, 71, 0.4), 0 4px 12px rgba(0, 0, 0, 0.2); }
  70% { box-shadow: 0 0 0 6px rgba(253, 224, 71, 0), 0 4px 12px rgba(0, 0, 0, 0.2); }
  100% { box-shadow: 0 0 0 0 rgba(253, 224, 71, 0), 0 4px 12px rgba(0, 0, 0, 0.2); }
} 
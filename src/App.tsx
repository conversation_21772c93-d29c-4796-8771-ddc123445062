import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import Landing from "./pages/Landing";
import Login from "./pages/Login";
import LMS from "./pages/LMS";
import TrenningLMS from "./pages/LMS/index";
import CreateLearningPath from "./pages/LMS/CreateLearningPath";
import EditCourse from "./pages/LMS/EditCourse";
import QuizEditor from "./pages/LMS/QuizEditor";
import ContentCreator from "./pages/LMS/ContentCreator";
import CourseBuilder from "./pages/LMS/CourseBuilder";
import Reports from "./pages/LMS/Reports";
import CourseDetail from "./pages/LMS/CourseDetail";
import LMSAdmin from "./pages/LMS/Admin";
import PhishingSimulationManager from "./pages/LMS/PhishingSimulationManager";
import GraphicalEditor from "./pages/documents/GraphicalEditor";

import { LanguageProvider } from "./providers/LanguageProvider";
import { ThemeProvider } from "./providers/ThemeProvider";
import { ZoomProvider } from "@/components/ui/zoom-toggle";

const queryClient = new QueryClient();

// Check if we're on GitHub Pages
const isGitHubPages = window.location.hostname.includes('github.io');
// Use the appropriate basename based on deployment platform
const basename = import.meta.env.DEV
  ? "/"
  : isGitHubPages
    ? "/audit-readiness-hub/"
    : "/";

// EmptyLayout removed as it was unused

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider>
      <ZoomProvider>
        <LanguageProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter basename={basename}>
              <Routes>
                {/* Pages with main app layout */}
                <Route path="/" element={<Landing />} />
                <Route path="/login" element={<Login />} />
                <Route path="/app/*" element={<Index />} />

                {/* Standalone editor routes */}
                <Route path="/editor" element={<GraphicalEditor />} />

                {/* LMS routes */}
                <Route path="/lms-old" element={<LMS />} />
                <Route path="/lms" element={<TrenningLMS />} />
                <Route path="/lms/admin" element={<LMSAdmin />} />
                <Route path="/lms/create/learning-path" element={<CreateLearningPath />} />
                <Route path="/lms/create/content" element={<ContentCreator />} />
                <Route path="/lms/create/course-builder" element={<CourseBuilder />} />
                <Route path="/lms/courses/edit" element={<EditCourse />} />
                <Route path="/lms/quizzes/create" element={<QuizEditor />} />
                <Route path="/lms/quizzes/edit/:id" element={<QuizEditor />} />
                <Route path="/lms/reports" element={<Reports />} />
                <Route path="/lms/course/:courseId" element={<CourseDetail />} />
                <Route path="/lms/phishing-simulation-manager" element={<PhishingSimulationManager />} />
                <Route path="*" element={<NotFound />} />
              </Routes>
            </BrowserRouter>
          </TooltipProvider>
        </LanguageProvider>
      </ZoomProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;

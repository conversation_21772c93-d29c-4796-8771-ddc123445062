@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 33% 98%;
    --foreground: 210 30% 16%;

    --card: 0 0% 100%;
    --card-foreground: 210 30% 16%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 30% 16%;

    --primary: 210 50% 28%;
    --primary-foreground: 210 20% 98%;

    --secondary: 200 45% 47%;
    --secondary-foreground: 0 0% 100%;

    --muted: 210 20% 92%;
    --muted-foreground: 210 20% 40%;

    --accent: 160 80% 48%;
    --accent-foreground: 0 0% 0%;

    --destructive: 340 80% 45%;
    --destructive-foreground: 210 20% 98%;

    --border: 215 20% 87%;
    --border-width: 1px;
    --border-radius: 0.75rem;

    --input: 210 30% 25%;
    --ring: 210 50% 50%;

    --radius: 0.5rem;

    --sidebar-background: 210 37% 16%;
    --sidebar-foreground: 210 20% 98%;
    --sidebar-primary: 210 50% 28%;
    --sidebar-primary-foreground: 210 20% 98%;
    --sidebar-accent: 210 30% 25%;
    --sidebar-accent-foreground: 210 20% 98%;
    --sidebar-border: 210 30% 25%;
    --sidebar-ring: 210 50% 28%;
  }

  .dark {
    --background: 210 30% 16%;
    --foreground: 210 20% 98%;

    --card: 210 37% 16%;
    --card-foreground: 210 20% 98%;

    --popover: 210 37% 16%;
    --popover-foreground: 210 20% 98%;

    --primary: 210 50% 28%;
    --primary-foreground: 210 20% 98%;

    --secondary: 200 45% 47%;
    --secondary-foreground: 0 0% 100%;

    --muted: 210 30% 25%;
    --muted-foreground: 210 20% 70%;

    --accent: 160 80% 48%;
    --accent-foreground: 0 0% 0%;

    --destructive: 340 80% 45%;
    --destructive-foreground: 210 20% 98%;

    --border: 215 20% 20%;
    --input: 210 30% 25%;
    --ring: 210 50% 50%;
  }
}

@layer base {
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom styles for the dashboard */
.status-fulfilled {
  @apply text-green-500 bg-green-50 dark:bg-green-900/20;
}
.status-partially {
  @apply text-amber-500 bg-amber-50 dark:bg-amber-900/20;
}
.status-not-fulfilled {
  @apply text-red-500 bg-red-50 dark:bg-red-900/20;
}
.status-not-applicable {
  @apply text-gray-500 bg-gray-50 dark:bg-gray-900/20;
}

/* Layout elements z-index and positioning */
.main-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: hsl(var(--background));
  border-bottom: 1px solid hsl(var(--border));
}

.sidebar {
  z-index: 20;
}

.content-area {
  position: relative;
  z-index: 5;
}

/* Card styling */
.card, [data-card="true"] {
  border-color: hsl(var(--border));
  border-width: var(--border-width);
  border-style: solid;
  border-radius: var(--border-radius);
  box-shadow: 0 1.5px 6px rgba(30, 41, 59, 0.06);
}

/* Global dark mode improvements */
.dark {
  /* Subtler borders for all elements */
  --subtle-border-color: hsla(215, 25%, 27%, 0.6);
  --subtle-border-hover: hsla(215, 25%, 35%, 0.8);
  --subtle-highlight: hsla(210, 30%, 30%, 0.4);
  --subtle-separator: hsla(215, 25%, 22%, 0.8);
}

/* Improved dark mode card borders for better separation */
.dark .card,
.dark [data-card="true"],
.dark [role="dialog"],
.dark .border,
.dark [data-state="open"],
.dark [data-shadow="sm"],
.dark [data-shadow="md"],
.dark [data-shadow="lg"] {
  border-color: var(--subtle-border-color) !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.18), 0 1.5px 4px rgba(15,23,42,0.10) !important;
  border-width: 1px !important;
  border-style: solid !important;
  border-radius: 0.5rem !important;
}

/* Fix for dark mode table styling */
.dark table,
.dark th,
.dark td,
.dark tr,
.dark thead,
.dark tbody {
  border-color: var(--subtle-border-color) !important;
}

/* Fix for dark mode list styling */
.dark ul,
.dark ol,
.dark li,
.dark dl,
.dark dt,
.dark dd {
  border-color: var(--subtle-border-color) !important;
}

/* Fix for all dividers and separators */
.dark hr,
.dark .divider,
.dark .separator,
.dark [role="separator"] {
  border-color: var(--subtle-separator) !important;
}

/* Fix for header line to match navbar line */
.dark header,
.dark nav,
.dark aside,
.dark main > div:first-child {
  border-color: var(--subtle-border-color) !important;
}

/* Fix for main layout header in dark mode */
.dark main > div.border-b,
.dark aside.border-r,
.dark aside.border-l,
.dark nav.border-b,
.dark nav.border-r,
.dark nav.border-l,
.dark header.border-b {
  border-color: var(--subtle-border-color) !important;
  border-width: 1px !important;
}

/* Dashboard specific improvements for dark mode */
.dark .upcoming-tasks,
.dark .compliance-status,
.dark .recent-assessments,
.dark .progress-indicator,
.dark .progress-bar,
.dark .chart-container {
  border-color: var(--subtle-border-color) !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
}

/* Ensure content alignment with navbar */
main {
  display: flex;
  flex-direction: column;
}

main > .content-area {
  padding-top: 1.25rem !important;
}

/* Ensure navbar and content borders align properly */
aside.border-r,
main > div.border-b {
  border-width: 1px !important;
}

/* Fix progress bars in dark mode */
.dark progress,
.dark .progress,
.dark [role="progressbar"] {
  background-color: var(--subtle-border-color) !important;
  border-color: var(--subtle-border-color) !important;
}

/* Specific fixes for dashboard cards */
.dark [class*="compliance-card"],
.dark [class*="assessment-card"],
.dark [class*="status-card"],
.dark [class*="metric-card"] {
  border-color: var(--subtle-border-color) !important;
  background-color: hsla(210, 30%, 18%, 0.4) !important;
}

/* Form elements in dark mode */
.dark input,
.dark select,
.dark textarea,
.dark button,
.dark .button,
.dark [role="button"],
.dark [type="text"],
.dark [type="password"],
.dark [type="email"],
.dark [type="number"],
.dark [type="search"],
.dark [type="tel"],
.dark [type="url"],
.dark [type="checkbox"],
.dark [type="radio"] {
  border-color: var(--subtle-border-color) !important;
}

.dark input:hover,
.dark select:hover,
.dark textarea:hover,
.dark button:hover,
.dark .button:hover,
.dark [role="button"]:hover {
  border-color: var(--subtle-border-hover) !important;
}

.dark input:focus,
.dark select:focus,
.dark textarea:focus {
  border-color: hsl(var(--ring)) !important;
  outline-color: hsl(var(--ring)) !important;
}

/* Print styles for PDF export */
@media print {
  @page {
    margin: 1cm;
    size: portrait;
  }
  
  body {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    background: white !important;
  }
  
  /* Hide UI elements not needed in print */
  button, 
  .print-hide, 
  header, 
  footer,
  nav,
  .collapsible-trigger,
  .tabs-list,
  .dialog-overlay,
  .dialog-content {
    display: none !important;
  }
  
  /* Ensure content is visible in print */
  .print-show {
    display: block !important;
  }
  
  /* Avoid bad page breaks */
  h1, h2, h3,
  .page-break-avoid,
  .card-header,
  table thead {
    page-break-after: avoid;
  }
  
  /* Control page breaks */
  .page-break-before {
    page-break-before: always;
  }
  
  .page-break-after {
    page-break-after: always;
  }
  
  .page-break-inside-avoid {
    page-break-inside: avoid;
  }
  
  /* Keep tables together */
  table {
    page-break-inside: avoid;
  }
  
  /* Keep images together */
  img {
    page-break-inside: avoid;
  }
  
  /* Fix for Tailwind dark mode in print */
  .dark\:bg-slate-900,
  .dark\:bg-slate-800,
  .dark\:bg-slate-950\/30,
  .dark\:bg-green-950\/30,
  .dark\:bg-amber-950\/30,
  .dark\:bg-red-950\/30,
  .dark\:bg-blue-950\/30 {
    background-color: white !important;
  }
  
  .dark\:text-slate-400,
  .dark\:text-green-400,
  .dark\:text-amber-400,
  .dark\:text-red-400,
  .dark\:text-blue-400 {
    color: #1f2937 !important; /* dark gray for better print contrast */
  }
  
  .dark\:border-slate-700,
  .dark\:border-slate-800 {
    border-color: #e5e7eb !important; /* light gray for borders */
  }
  
  /* Improve text contrast for printing */
  .text-muted-foreground {
    color: #4b5563 !important; /* darker gray for better print contrast */
  }

  /* Show collapse content in print */
  .collapsible-content {
    display: block !important;
    height: auto !important;
  }

  /* Show all tabs content in print */
  .tabs-content {
    display: block !important;
    margin-bottom: 1rem !important;
  }

  /* Ensure borders are visible */
  .border {
    border: 1px solid #e5e7eb !important;
  }

  /* Ensure card backgrounds are white */
  .card {
    background-color: white !important;
    box-shadow: none !important;
    border: 1px solid #e5e7eb !important;
    break-inside: avoid !important;
  }

  /* Status colors for badges in print */
  .status-badge[data-status="fulfilled"] {
    background-color: #ecfdf5 !important;
    color: #065f46 !important;
    border: 1px solid #10b981 !important;
  }
  
  .status-badge[data-status="partially-fulfilled"] {
    background-color: #fffbeb !important;
    color: #92400e !important;
    border: 1px solid #f59e0b !important;
  }
  
  .status-badge[data-status="not-fulfilled"] {
    background-color: #fef2f2 !important;
    color: #b91c1c !important;
    border: 1px solid #ef4444 !important;
  }
  
  .status-badge[data-status="not-applicable"] {
    background-color: #f9fafb !important;
    color: #6b7280 !important;
    border: 1px solid #9ca3af !important;
  }
}

/* Helper for PDF report container */
.pdf-container {
  background: white;
  color: black;
  width: 210mm; /* A4 width */
  min-height: 297mm; /* A4 height */
  margin: 0 auto;
  padding: 20mm;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* Additional styles for assessment PDF exports */
.printing-assessment .recharts-wrapper,
.printing-assessment .recharts-surface,
.printing-supplier-report .recharts-wrapper,
.printing-supplier-report .recharts-surface {
  overflow: visible !important;
}

.printing-assessment .recharts-legend-wrapper,
.printing-supplier-report .recharts-legend-wrapper {
  overflow: visible !important;
}

/* Assessment report specific print styles */
@media print {
  .assessment-report-content,
  .supplier-report-content {
    padding: 0 !important;
    margin: 0 !important;
    width: 100% !important;
    max-width: none !important;
  }
  
  .assessment-report-content h1,
  .supplier-report-content h1 {
    font-size: 24pt !important;
    margin-bottom: 10pt !important;
  }
  
  .assessment-report-content h2,
  .supplier-report-content h2 {
    font-size: 18pt !important;
    margin-top: 20pt !important;
    margin-bottom: 10pt !important;
  }
  
  .assessment-report-content h3,
  .supplier-report-content h3 {
    font-size: 14pt !important;
    margin-top: 15pt !important;
    margin-bottom: 8pt !important;
  }
  
  .assessment-report-content .requirement-card,
  .supplier-report-content .requirement-card {
    page-break-inside: avoid !important;
    margin-bottom: 15pt !important;
  }

  /* Ensure charts are visible */
  .recharts-wrapper {
    page-break-inside: avoid !important;
    margin: 0 auto !important;
  }

  /* Make sure status badges stand out */
  .status-badge {
    padding: 2px 6px !important;
    border-radius: 4px !important;
    font-weight: 600 !important;
    font-size: 11pt !important;
    display: inline-block !important;
  }

  /* Badge colors */
  .badge {
    font-size: 11pt !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
  }

  /* Stats cards */
  .bg-green-50, .bg-amber-50, .bg-red-50, .bg-slate-50, .bg-blue-50 {
    border: 1px solid #e5e7eb !important;
    padding: 8px !important;
  }

  /* Color text */
  .text-green-600 {
    color: #059669 !important;
  }
  
  .text-amber-600 {
    color: #d97706 !important;
  }
  
  .text-red-600 {
    color: #dc2626 !important;
  }
  
  .text-slate-600 {
    color: #475569 !important;
  }
}

/* Fix for supplier preview container */
.supplier-preview-wrapper {
  transform-origin: top left;
  transform: scale(0.85);
  height: 118%;
  overflow: hidden;
  background: white;
}

.supplier-preview-wrapper .supplier-report-content {
  border: none !important;
  padding: 16px !important;
  max-width: none !important;
  width: 100% !important;
}

/* Fix for PDF print and preview related issues */
@media print, (min-width: 1200px) {
  .supplier-preview-wrapper {
    transform: none;
    height: auto;
  }
}

/* Enhance Supplier PDF styles for a more professional look */
.supplier-report-content {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Professional table styling for PDF export */
.supplier-report-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  border-radius: 4px;
  overflow: hidden;
}

.supplier-report-content th {
  background-color: #f5f7fa;
  color: #334155;
  font-weight: 600;
  text-align: left;
  padding: 0.75rem 1rem;
  border-bottom: 2px solid #e2e8f0;
}

.supplier-report-content td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e2e8f0;
  color: #334155;
}

.supplier-report-content tr:last-child td {
  border-bottom: none;
}

/* Make the summary cards more visually appealing */
.supplier-report-content .grid-cols-4 > div {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

/* Enhanced PDF styles for print */
@media print {
  .supplier-report-content {
    color: #1e293b;
    font-size: 11pt;
    line-height: 1.5;
  }
  
  .supplier-report-content h1 {
    font-size: 20pt;
    margin-bottom: 15pt;
    color: #0f172a;
  }
  
  .supplier-report-content h2 {
    font-size: 16pt;
    margin-top: 16pt;
    margin-bottom: 8pt;
    color: #0f172a;
    page-break-after: avoid;
  }
  
  .supplier-report-content h3 {
    font-size: 14pt;
    margin-top: 14pt;
    margin-bottom: 7pt;
    color: #0f172a;
    page-break-after: avoid;
  }
  
  .supplier-report-content p {
    orphans: 3;
    widows: 3;
  }
  
  .supplier-report-content table {
    font-size: 10pt;
    border: 1px solid #cbd5e1;
    page-break-inside: avoid;
  }
  
  .supplier-report-content th {
    background-color: #f1f5f9 !important;
    color: #0f172a !important;
    font-weight: 700;
    border-bottom: 1.5pt solid #64748b !important;
  }
  
  .supplier-report-content td {
    border-bottom: 0.5pt solid #cbd5e1 !important;
  }
  
  /* Better status badge coloring in print */
  .supplier-report-content [data-status] {
    font-weight: 600;
    padding: 3pt 6pt;
    border-radius: 3pt;
  }
  
  /* Keep cards together and make them stand out */
  .supplier-report-content .card {
    break-inside: avoid;
    border: 1pt solid #cbd5e1 !important;
    margin-bottom: 12pt;
  }
  
  /* Ensure all data fits properly */
  .supplier-report-content .overflow-hidden {
    overflow: visible !important;
  }
}

/* Removed all non-scoped styles that were causing conflicts */

/* <PERSON>ope all ReactFlow styles to only apply inside the organization chart component */
.organizational-chart-component .react-flow__viewport {
  background-color: #ffffff !important;
  background-image: none !important;
}

/* Node styles */
.organizational-chart-component .react-flow__node {
  padding: 10px;
  border-radius: 3px;
  width: 150px;
  font-size: 12px;
  color: #222;
  text-align: center;
  border-width: 1px;
  border-style: solid;
  background: white;
  transition: transform 0.1s ease;
}

.organizational-chart-component .react-flow__node-organization {
  border-color: #1a192b;
}

/* Handle styles */
.organizational-chart-component .react-flow__handle {
  width: 8px;
  height: 8px;
  background: #1a192b;
}

.organizational-chart-component .react-flow__handle-top {
  top: -4px;
}

.organizational-chart-component .react-flow__handle-bottom {
  bottom: -4px;
}

/* Edge styles */
.organizational-chart-component .react-flow__edge-path {
  stroke: #b1b1b7;
  stroke-width: 2;
}

.organizational-chart-component .react-flow__edge-text {
  font-size: 10px;
}

.organizational-chart-component .react-flow__edge-textbg {
  fill: white;
}

.organizational-chart-component .react-flow__connection-path {
  stroke: #b1b1b7;
}

/* Selection and hover states */
.organizational-chart-component .react-flow__node.selected {
  box-shadow: 0 0 0 2px #1a192b;
}

.organizational-chart-component .react-flow__node.selectable:hover {
  box-shadow: 0 1px 4px 1px rgba(0, 0, 0, 0.08);
}

.organizational-chart-component .react-flow__node.dragging {
  box-shadow: 0 0 0 2px #1a192b;
  transform: scale(1.02);
}

/* Additional styles to handle any remaining white boxes */
.organizational-chart-component .react-flow__renderer {
  background-color: transparent !important;
}

.organizational-chart-component .react-flow__pane {
  background-color: transparent !important;
}

.organizational-chart-component .react-flow__container {
  background-color: transparent !important;
}

.organizational-chart-component .react-flow__minimap {
  background-color: transparent !important;
} 
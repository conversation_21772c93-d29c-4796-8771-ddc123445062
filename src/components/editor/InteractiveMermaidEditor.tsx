/**
 * Interactive Mermaid Editor with React Flow
 * Drag-and-drop visual editing that generates Mermaid code
 * Following user requirements: white/blue theme, no visible code, interactive editing
 */

import React, { useState, useCallback, useMemo } from 'react';
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  BackgroundVariant,
  MiniMap,
  Panel,
  NodeTypes,
  EdgeTypes
} from 'reactflow';
import 'reactflow/dist/style.css';

import {
  Plus, Square, Circle, Diamond, ArrowRight,
  Type, Palette, Download, Undo, Redo, ZoomIn, ZoomOut
} from 'lucide-react';
import {
  GlassPanel,
  GlassButton,
  MermaidDesignTokens
} from '../ui';

// Custom Node Types for different shapes
const CustomNode: React.FC<any> = ({ data, selected }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [label, setLabel] = useState(data.label || 'Node');

  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  const handleLabelChange = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      setIsEditing(false);
      data.onLabelChange?.(data.id, label);
    }
  };

  const getNodeStyle = () => {
    const baseStyle = {
      padding: '12px 16px',
      borderRadius: data.shape === 'circle' ? '50%' : data.shape === 'diamond' ? '0' : '8px',
      background: selected ? MermaidDesignTokens.colors.accent.blue : MermaidDesignTokens.colors.glass.primary,
      border: `2px solid ${selected ? MermaidDesignTokens.colors.accent.darkBlue : MermaidDesignTokens.colors.glass.border}`,
      color: MermaidDesignTokens.colors.text.primary,
      fontSize: MermaidDesignTokens.typography.fontSize.sm,
      fontWeight: MermaidDesignTokens.typography.fontWeight.medium,
      minWidth: '80px',
      textAlign: 'center' as const,
      cursor: 'pointer',
      transition: 'all 0.2s ease'
    };

    if (data.shape === 'diamond') {
      return {
        ...baseStyle,
        transform: 'rotate(45deg)',
        borderRadius: '8px'
      };
    }

    return baseStyle;
  };

  return (
    <div style={getNodeStyle()} onDoubleClick={handleDoubleClick}>
      {isEditing ? (
        <input
          type="text"
          value={label}
          onChange={(e) => setLabel(e.target.value)}
          onKeyDown={handleLabelChange}
          onBlur={() => setIsEditing(false)}
          autoFocus
          style={{
            background: 'transparent',
            border: 'none',
            outline: 'none',
            color: 'inherit',
            fontSize: 'inherit',
            fontWeight: 'inherit',
            textAlign: 'center',
            width: '100%'
          }}
        />
      ) : (
        <span>{label}</span>
      )}
    </div>
  );
};

// Node types
const nodeTypes: NodeTypes = {
  custom: CustomNode
};

// Initial nodes and edges
const initialNodes: Node[] = [
  {
    id: '1',
    type: 'custom',
    position: { x: 250, y: 100 },
    data: { 
      label: 'Start Process',
      shape: 'rectangle',
      onLabelChange: () => {}
    }
  },
  {
    id: '2',
    type: 'custom',
    position: { x: 250, y: 200 },
    data: { 
      label: 'Decision Point',
      shape: 'diamond',
      onLabelChange: () => {}
    }
  },
  {
    id: '3',
    type: 'custom',
    position: { x: 150, y: 300 },
    data: { 
      label: 'Process A',
      shape: 'rectangle',
      onLabelChange: () => {}
    }
  },
  {
    id: '4',
    type: 'custom',
    position: { x: 350, y: 300 },
    data: { 
      label: 'Process B',
      shape: 'rectangle',
      onLabelChange: () => {}
    }
  }
];

const initialEdges: Edge[] = [
  { id: 'e1-2', source: '1', target: '2', animated: true },
  { id: 'e2-3', source: '2', target: '3', label: 'Yes' },
  { id: 'e2-4', source: '2', target: '4', label: 'No' }
];

interface InteractiveMermaidEditorProps {
  onMermaidCodeChange?: (code: string) => void;
}

export const InteractiveMermaidEditor: React.FC<InteractiveMermaidEditorProps> = ({
  onMermaidCodeChange
}) => {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedTool, setSelectedTool] = useState<'select' | 'rectangle' | 'circle' | 'diamond'>('select');
  const [nodeIdCounter, setNodeIdCounter] = useState(5);

  // Generate Mermaid code from current nodes and edges
  const generateMermaidCode = useCallback(() => {
    let code = 'flowchart TD\n';
    
    // Add nodes with their labels
    nodes.forEach(node => {
      const label = node.data.label || 'Node';
      const shape = node.data.shape || 'rectangle';
      
      switch (shape) {
        case 'circle':
          code += `    ${node.id}((${label}))\n`;
          break;
        case 'diamond':
          code += `    ${node.id}{${label}}\n`;
          break;
        default:
          code += `    ${node.id}[${label}]\n`;
      }
    });
    
    // Add edges
    edges.forEach(edge => {
      const label = edge.label ? `|${edge.label}|` : '';
      code += `    ${edge.source} -->${label} ${edge.target}\n`;
    });

    // Add styling
    code += '\n    %% Styling\n';
    nodes.forEach(node => {
      code += `    style ${node.id} fill:#2563eb,stroke:#1d4ed8,stroke-width:2px,color:#fff\n`;
    });

    return code;
  }, [nodes, edges]);

  // Update Mermaid code when nodes or edges change
  React.useEffect(() => {
    const code = generateMermaidCode();
    onMermaidCodeChange?.(code);
  }, [nodes, edges, generateMermaidCode, onMermaidCodeChange]);

  // Handle new connections
  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  // Handle node label changes
  const handleNodeLabelChange = useCallback((nodeId: string, newLabel: string) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId
          ? { ...node, data: { ...node.data, label: newLabel } }
          : node
      )
    );
  }, [setNodes]);

  // Add new node
  const addNode = useCallback((shape: 'rectangle' | 'circle' | 'diamond') => {
    const newNode: Node = {
      id: nodeIdCounter.toString(),
      type: 'custom',
      position: { x: Math.random() * 400 + 100, y: Math.random() * 300 + 100 },
      data: {
        label: `New ${shape}`,
        shape,
        onLabelChange: handleNodeLabelChange
      }
    };
    
    setNodes((nds) => [...nds, newNode]);
    setNodeIdCounter(prev => prev + 1);
  }, [nodeIdCounter, setNodes, handleNodeLabelChange]);

  // Update existing nodes with label change handler
  React.useEffect(() => {
    setNodes((nds) =>
      nds.map((node) => ({
        ...node,
        data: { ...node.data, onLabelChange: handleNodeLabelChange }
      }))
    );
  }, [handleNodeLabelChange, setNodes]);

  return (
    <div style={{ 
      height: '100%', 
      width: '100%', 
      background: MermaidDesignTokens.colors.secondary.gradient,
      position: 'relative'
    }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        fitView
        style={{ background: MermaidDesignTokens.colors.secondary.gradient }}
      >
        <Background 
          variant={BackgroundVariant.Dots} 
          gap={20} 
          size={1}
          color={MermaidDesignTokens.colors.glass.border}
        />
        
        <Controls 
          style={{
            background: MermaidDesignTokens.colors.glass.primary,
            border: `1px solid ${MermaidDesignTokens.colors.glass.border}`,
            borderRadius: MermaidDesignTokens.borderRadius.lg
          }}
        />
        
        <MiniMap 
          style={{
            background: MermaidDesignTokens.colors.glass.primary,
            border: `1px solid ${MermaidDesignTokens.colors.glass.border}`,
            borderRadius: MermaidDesignTokens.borderRadius.lg
          }}
          nodeColor={MermaidDesignTokens.colors.accent.blue}
        />

        {/* Toolbar Panel */}
        <Panel position="top-left">
          <GlassPanel variant="elevated" padding="2" style={{
            display: 'flex',
            alignItems: 'center',
            gap: MermaidDesignTokens.spacing[2]
          }}>
            <GlassButton
              variant={selectedTool === 'rectangle' ? 'primary' : 'ghost'}
              size="sm"
              icon={<Square size={16} />}
              onClick={() => {
                setSelectedTool('rectangle');
                addNode('rectangle');
              }}
              title="Add Rectangle"
            />
            <GlassButton
              variant={selectedTool === 'circle' ? 'primary' : 'ghost'}
              size="sm"
              icon={<Circle size={16} />}
              onClick={() => {
                setSelectedTool('circle');
                addNode('circle');
              }}
              title="Add Circle"
            />
            <GlassButton
              variant={selectedTool === 'diamond' ? 'primary' : 'ghost'}
              size="sm"
              icon={<Diamond size={16} />}
              onClick={() => {
                setSelectedTool('diamond');
                addNode('diamond');
              }}
              title="Add Diamond"
            />
            
            <div style={{
              width: '1px',
              height: '24px',
              background: MermaidDesignTokens.colors.glass.border
            }} />
            
            <GlassButton
              variant="ghost"
              size="sm"
              icon={<Type size={16} />}
              title="Edit Text (Double-click nodes)"
            />
            <GlassButton
              variant="ghost"
              size="sm"
              icon={<Download size={16} />}
              title="Export"
            />
          </GlassPanel>
        </Panel>

        {/* Instructions Panel */}
        <Panel position="bottom-center">
          <GlassPanel variant="secondary" padding="3" style={{
            textAlign: 'center',
            maxWidth: '400px'
          }}>
            <p style={{
              fontSize: MermaidDesignTokens.typography.fontSize.sm,
              color: MermaidDesignTokens.colors.text.secondary,
              margin: 0,
              lineHeight: MermaidDesignTokens.typography.lineHeight.relaxed
            }}>
              <strong>Interactive Editing:</strong> Drag nodes to move • Double-click to edit text • 
              Connect nodes by dragging from handles • Click toolbar to add shapes
            </p>
          </GlassPanel>
        </Panel>
      </ReactFlow>
    </div>
  );
};

export default InteractiveMermaidEditor;

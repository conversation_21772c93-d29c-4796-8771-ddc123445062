/**
 * Interactive Mermaid Editor with React Flow
 * Drag-and-drop visual editing that generates Mermaid code
 * Following user requirements: white/blue theme, no visible code, interactive editing
 */

import React, { useState, useCallback, useMemo } from 'react';
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  BackgroundVariant,
  MiniMap,
  Panel,
  NodeTypes,
  EdgeTypes
} from 'reactflow';
import 'reactflow/dist/style.css';

import {
  Plus, Square, Circle, Diamond, ArrowRight,
  Type, Palette, Download, Undo, Redo, ZoomIn, ZoomOut,
  Brain, Sparkles
} from 'lucide-react';
import {
  GlassPanel,
  GlassButton,
  GlassInput,
  MermaidDesignTokens
} from '../ui';
import { NodePropertiesPanel } from './NodePropertiesPanel';
import { MermaidAIService } from '../../services/ai/MermaidAIService';

// Custom Node Types for different shapes
const CustomNode: React.FC<any> = ({ data, selected }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [label, setLabel] = useState(data.label || 'Node');

  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  const handleLabelChange = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      setIsEditing(false);
      data.onLabelChange?.(data.id, label);
    }
  };

  const getNodeStyle = () => {
    const fillColor = data.fillColor || (selected ? MermaidDesignTokens.colors.accent.blue : MermaidDesignTokens.colors.glass.primary);
    const strokeColor = data.strokeColor || (selected ? MermaidDesignTokens.colors.accent.darkBlue : MermaidDesignTokens.colors.glass.border);
    const strokeWidth = data.strokeWidth || 2;
    const textColor = data.textColor || MermaidDesignTokens.colors.text.primary;

    const baseStyle = {
      padding: '12px 16px',
      background: fillColor,
      border: `${strokeWidth}px solid ${strokeColor}`,
      color: textColor,
      fontSize: MermaidDesignTokens.typography.fontSize.sm,
      fontWeight: MermaidDesignTokens.typography.fontWeight.medium,
      minWidth: '80px',
      textAlign: 'center' as const,
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      boxShadow: selected ? MermaidDesignTokens.shadows.glow.blue : 'none'
    };

    // Shape-specific styling following Mermaid best practices
    switch (data.shape) {
      case 'circle':
        return {
          ...baseStyle,
          borderRadius: '50%',
          width: '80px',
          height: '80px',
          padding: '0'
        };
      case 'diamond':
        return {
          ...baseStyle,
          width: '80px',
          height: '80px',
          padding: '0',
          clipPath: 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)',
          borderRadius: '0'
        };
      default: // rectangle
        return {
          ...baseStyle,
          borderRadius: '8px'
        };
    }
  };

  return (
    <div style={getNodeStyle()} onDoubleClick={handleDoubleClick}>
      {isEditing ? (
        <input
          type="text"
          value={label}
          onChange={(e) => setLabel(e.target.value)}
          onKeyDown={handleLabelChange}
          onBlur={() => setIsEditing(false)}
          autoFocus
          style={{
            background: 'transparent',
            border: 'none',
            outline: 'none',
            color: 'inherit',
            fontSize: 'inherit',
            fontWeight: 'inherit',
            textAlign: 'center',
            width: '100%'
          }}
        />
      ) : (
        <span>{label}</span>
      )}
    </div>
  );
};

// Node types
const nodeTypes: NodeTypes = {
  custom: CustomNode
};

// Initial nodes and edges
const initialNodes: Node[] = [
  {
    id: '1',
    type: 'custom',
    position: { x: 250, y: 100 },
    data: {
      label: 'Start Process',
      shape: 'rectangle',
      onLabelChange: () => {}
    }
  },
  {
    id: '2',
    type: 'custom',
    position: { x: 250, y: 200 },
    data: {
      label: 'Decision Point',
      shape: 'diamond',
      onLabelChange: () => {}
    }
  },
  {
    id: '3',
    type: 'custom',
    position: { x: 150, y: 300 },
    data: {
      label: 'Process A',
      shape: 'rectangle',
      onLabelChange: () => {}
    }
  },
  {
    id: '4',
    type: 'custom',
    position: { x: 350, y: 300 },
    data: {
      label: 'Process B',
      shape: 'rectangle',
      onLabelChange: () => {}
    }
  }
];

const initialEdges: Edge[] = [
  { id: 'e1-2', source: '1', target: '2', animated: true },
  { id: 'e2-3', source: '2', target: '3', label: 'Yes' },
  { id: 'e2-4', source: '2', target: '4', label: 'No' }
];

interface InteractiveMermaidEditorProps {
  onMermaidCodeChange?: (code: string) => void;
}

export const InteractiveMermaidEditor: React.FC<InteractiveMermaidEditorProps> = ({
  onMermaidCodeChange
}) => {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedTool, setSelectedTool] = useState<'select' | 'rectangle' | 'circle' | 'diamond'>('select');
  const [nodeIdCounter, setNodeIdCounter] = useState(5);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [showPropertiesPanel, setShowPropertiesPanel] = useState(false);
  const [aiPrompt, setAiPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  // Generate Mermaid code from current nodes and edges following official syntax
  const generateMermaidCode = useCallback(() => {
    let code = 'flowchart TD\n';

    // Add nodes with their labels using proper Mermaid syntax
    nodes.forEach(node => {
      const label = node.data.label || 'Node';
      const shape = node.data.shape || 'rectangle';

      // Following official Mermaid syntax from documentation
      switch (shape) {
        case 'circle':
          code += `    ${node.id}((${label}))\n`;
          break;
        case 'diamond':
          code += `    ${node.id}{${label}}\n`;
          break;
        case 'rectangle':
        default:
          code += `    ${node.id}[${label}]\n`;
      }
    });

    // Add edges with proper arrow syntax
    edges.forEach(edge => {
      const label = edge.label ? ` -- ${edge.label} -->` : ' -->';
      code += `    ${edge.source}${label} ${edge.target}\n`;
    });

    // Add professional styling following Mermaid best practices
    code += '\n    %% Professional Styling\n';
    nodes.forEach(node => {
      const shape = node.data.shape || 'rectangle';
      switch (shape) {
        case 'circle':
          code += `    style ${node.id} fill:#3b82f6,stroke:#1d4ed8,stroke-width:2px,color:#fff\n`;
          break;
        case 'diamond':
          code += `    style ${node.id} fill:#f59e0b,stroke:#d97706,stroke-width:2px,color:#fff\n`;
          break;
        default:
          code += `    style ${node.id} fill:#2563eb,stroke:#1d4ed8,stroke-width:2px,color:#fff\n`;
      }
    });

    return code;
  }, [nodes, edges]);

  // Update Mermaid code when nodes or edges change
  React.useEffect(() => {
    const code = generateMermaidCode();
    onMermaidCodeChange?.(code);
  }, [nodes, edges, generateMermaidCode, onMermaidCodeChange]);

  // Handle new connections
  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  // Handle node label changes
  const handleNodeLabelChange = useCallback((nodeId: string, newLabel: string) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId
          ? { ...node, data: { ...node.data, label: newLabel } }
          : node
      )
    );
  }, [setNodes]);

  // Add new node with default properties
  const addNode = useCallback((shape: 'rectangle' | 'circle' | 'diamond') => {
    const defaultColors = {
      rectangle: { fill: '#2563eb', stroke: '#1d4ed8' },
      circle: { fill: '#3b82f6', stroke: '#1d4ed8' },
      diamond: { fill: '#f59e0b', stroke: '#d97706' }
    };

    const newNode: Node = {
      id: nodeIdCounter.toString(),
      type: 'custom',
      position: { x: Math.random() * 400 + 100, y: Math.random() * 300 + 100 },
      data: {
        label: `New ${shape}`,
        shape,
        fillColor: defaultColors[shape].fill,
        strokeColor: defaultColors[shape].stroke,
        strokeWidth: 2,
        textColor: '#ffffff',
        onLabelChange: handleNodeLabelChange
      }
    };

    setNodes((nds) => [...nds, newNode]);
    setNodeIdCounter(prev => prev + 1);
  }, [nodeIdCounter, setNodes, handleNodeLabelChange]);

  // Handle node selection
  const handleNodeClick = useCallback((event: any, node: Node) => {
    setSelectedNode(node);
    setShowPropertiesPanel(true);
  }, []);

  // Update node properties
  const handleNodeUpdate = useCallback((nodeId: string, updates: any) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId
          ? { ...node, data: { ...node.data, ...updates } }
          : node
      )
    );
  }, [setNodes]);

  // Delete node
  const handleNodeDelete = useCallback((nodeId: string) => {
    setNodes((nds) => nds.filter((node) => node.id !== nodeId));
    setEdges((eds) => eds.filter((edge) => edge.source !== nodeId && edge.target !== nodeId));
    setSelectedNode(null);
    setShowPropertiesPanel(false);
  }, [setNodes, setEdges]);

  // Duplicate node
  const handleNodeDuplicate = useCallback((nodeId: string) => {
    const nodeToClone = nodes.find(n => n.id === nodeId);
    if (!nodeToClone) return;

    const newNode: Node = {
      ...nodeToClone,
      id: nodeIdCounter.toString(),
      position: {
        x: nodeToClone.position.x + 100,
        y: nodeToClone.position.y + 50
      }
    };

    setNodes((nds) => [...nds, newNode]);
    setNodeIdCounter(prev => prev + 1);
  }, [nodes, nodeIdCounter, setNodes]);

  // Handle AI generation
  const handleAIGenerate = useCallback(async () => {
    if (!aiPrompt.trim()) return;

    setIsGenerating(true);
    try {
      const aiService = MermaidAIService.getInstance();
      const response = await aiService.generateDiagram({
        prompt: aiPrompt,
        context: 'process',
        complexity: 'medium',
        style: 'professional'
      });

      // Convert Mermaid code to React Flow nodes and edges
      // For now, we'll just pass the code to the parent
      onMermaidCodeChange?.(response.mermaidCode);

      // Clear the prompt
      setAiPrompt('');
    } catch (error) {
      console.error('AI generation failed:', error);
    } finally {
      setIsGenerating(false);
    }
  }, [aiPrompt, onMermaidCodeChange]);

  // Update existing nodes with label change handler
  React.useEffect(() => {
    setNodes((nds) =>
      nds.map((node) => ({
        ...node,
        data: { ...node.data, onLabelChange: handleNodeLabelChange }
      }))
    );
  }, [handleNodeLabelChange, setNodes]);

  return (
    <div style={{
      height: '100%',
      width: '100%',
      background: MermaidDesignTokens.colors.secondary.gradient,
      position: 'relative'
    }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeClick={handleNodeClick}
        nodeTypes={nodeTypes}
        fitView
        style={{ background: MermaidDesignTokens.colors.secondary.gradient }}
      >
        <Background
          variant={BackgroundVariant.Dots}
          gap={20}
          size={1}
          color={MermaidDesignTokens.colors.glass.border}
        />

        <Controls
          style={{
            background: MermaidDesignTokens.colors.glass.primary,
            border: `1px solid ${MermaidDesignTokens.colors.glass.border}`,
            borderRadius: MermaidDesignTokens.borderRadius.lg
          }}
        />

        <MiniMap
          style={{
            background: MermaidDesignTokens.colors.glass.primary,
            border: `1px solid ${MermaidDesignTokens.colors.glass.border}`,
            borderRadius: MermaidDesignTokens.borderRadius.lg
          }}
          nodeColor={MermaidDesignTokens.colors.accent.blue}
        />

        {/* Floating Toolbar - Center Top like Design Folder */}
        <Panel position="top-center">
          <GlassPanel variant="elevated" padding="3" style={{
            display: 'flex',
            alignItems: 'center',
            gap: MermaidDesignTokens.spacing[3],
            borderRadius: MermaidDesignTokens.borderRadius['2xl'],
            boxShadow: MermaidDesignTokens.shadows.glass.xl
          }}>
            {/* Add Node Button */}
            <GlassButton
              variant="primary"
              size="sm"
              icon={<Plus size={18} />}
              onClick={() => addNode('rectangle')}
              style={{ fontWeight: MermaidDesignTokens.typography.fontWeight.semibold }}
              title="Add Rectangle Node (default)"
            >
              Add Node
            </GlassButton>

            {/* Shape Tools */}
            <div style={{ display: 'flex', alignItems: 'center', gap: MermaidDesignTokens.spacing[1] }}>
              <GlassButton
                variant={selectedTool === 'rectangle' ? 'primary' : 'ghost'}
                size="sm"
                icon={<Square size={18} />}
                onClick={() => {
                  setSelectedTool('rectangle');
                  addNode('rectangle');
                }}
                title="Add Rectangle"
              />
              <GlassButton
                variant={selectedTool === 'circle' ? 'primary' : 'ghost'}
                size="sm"
                icon={<Circle size={18} />}
                onClick={() => {
                  setSelectedTool('circle');
                  addNode('circle');
                }}
                title="Add Circle"
              />
              <GlassButton
                variant={selectedTool === 'diamond' ? 'primary' : 'ghost'}
                size="sm"
                icon={<Diamond size={18} />}
                onClick={() => {
                  setSelectedTool('diamond');
                  addNode('diamond');
                }}
                title="Add Diamond"
              />
            </div>

            <div style={{
              width: '1px',
              height: '32px',
              background: MermaidDesignTokens.colors.glass.border
            }} />

            {/* Edit Tools */}
            <div style={{ display: 'flex', alignItems: 'center', gap: MermaidDesignTokens.spacing[1] }}>
              <GlassButton
                variant="ghost"
                size="sm"
                icon={<Type size={18} />}
                title="Edit Text (Double-click nodes)"
              />
              <GlassButton
                variant="ghost"
                size="sm"
                icon={<Palette size={18} />}
                title="Colors & Styling"
              />
              <GlassButton
                variant="ghost"
                size="sm"
                icon={<Download size={18} />}
                title="Export Diagram"
              />
            </div>
          </GlassPanel>
        </Panel>

        {/* Enhanced Floating AI Input - Bottom Center */}
        <Panel position="bottom-center">
          <GlassPanel variant="elevated" padding="4" style={{
            display: 'flex',
            flexDirection: 'column',
            gap: MermaidDesignTokens.spacing[3],
            borderRadius: MermaidDesignTokens.borderRadius['2xl'],
            boxShadow: MermaidDesignTokens.shadows.glass.xl,
            minWidth: '600px',
            maxWidth: '800px'
          }}>
            {/* AI Input Header */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: MermaidDesignTokens.spacing[2]
            }}>
              <Brain size={20} color={MermaidDesignTokens.colors.accent.blue} />
              <h4 style={{
                fontSize: MermaidDesignTokens.typography.fontSize.sm,
                fontWeight: MermaidDesignTokens.typography.fontWeight.semibold,
                color: MermaidDesignTokens.colors.text.primary,
                margin: 0
              }}>
                AI Diagram Generator
              </h4>
              <div style={{
                fontSize: MermaidDesignTokens.typography.fontSize.xs,
                color: MermaidDesignTokens.colors.text.secondary,
                background: MermaidDesignTokens.colors.glass.secondary,
                padding: '2px 8px',
                borderRadius: MermaidDesignTokens.borderRadius.base
              }}>
                Beta
              </div>
            </div>

            {/* AI Input Field */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: MermaidDesignTokens.spacing[3]
            }}>
              <GlassInput
                placeholder="Describe your process, workflow, or system architecture..."
                value={aiPrompt}
                onChange={(e) => setAiPrompt(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleAIGenerate()}
                style={{
                  flex: 1,
                  background: MermaidDesignTokens.colors.glass.secondary,
                  border: `1px solid ${MermaidDesignTokens.colors.glass.border}`,
                  fontSize: MermaidDesignTokens.typography.fontSize.base,
                  padding: '12px 16px'
                }}
                icon={<Type size={16} />}
              />
              <GlassButton
                variant="primary"
                size="base"
                icon={isGenerating ? <div className="animate-spin">⟳</div> : <Sparkles size={18} />}
                onClick={handleAIGenerate}
                disabled={!aiPrompt.trim() || isGenerating}
                glow
                style={{ minWidth: '120px' }}
              >
                {isGenerating ? 'Generating...' : 'Generate'}
              </GlassButton>
            </div>

            {/* Quick Suggestions */}
            <div style={{
              display: 'flex',
              gap: MermaidDesignTokens.spacing[2],
              flexWrap: 'wrap'
            }}>
              {[
                'Security audit process',
                'Risk assessment workflow',
                'Incident response plan',
                'Compliance checklist',
                'Project timeline'
              ].map((suggestion) => (
                <button
                  key={suggestion}
                  onClick={() => setAiPrompt(suggestion)}
                  style={{
                    fontSize: MermaidDesignTokens.typography.fontSize.xs,
                    color: MermaidDesignTokens.colors.text.secondary,
                    background: MermaidDesignTokens.colors.glass.secondary,
                    border: `1px solid ${MermaidDesignTokens.colors.glass.border}`,
                    borderRadius: MermaidDesignTokens.borderRadius.base,
                    padding: '4px 8px',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = MermaidDesignTokens.colors.glass.primary;
                    e.currentTarget.style.color = MermaidDesignTokens.colors.text.primary;
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = MermaidDesignTokens.colors.glass.secondary;
                    e.currentTarget.style.color = MermaidDesignTokens.colors.text.secondary;
                  }}
                >
                  {suggestion}
                </button>
              ))}
            </div>
          </GlassPanel>
        </Panel>
      </ReactFlow>

      {/* Node Properties Panel */}
      <NodePropertiesPanel
        selectedNode={selectedNode}
        onNodeUpdate={handleNodeUpdate}
        onNodeDelete={handleNodeDelete}
        onNodeDuplicate={handleNodeDuplicate}
        isVisible={showPropertiesPanel}
        onClose={() => setShowPropertiesPanel(false)}
      />
    </div>
  );
};

export default InteractiveMermaidEditor;

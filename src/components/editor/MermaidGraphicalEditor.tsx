/**
 * AuditReady Mermaid Editor - TEXT-ONLY APPROACH
 * Following official Mermaid Live Editor: https://mermaid.live/
 *
 * KEY INSIGHT: Mermaid.js does NOT support visual editing!
 * - No drag and drop
 * - No visual node manipulation
 * - Text-based editing ONLY
 * - This is by design and cannot be changed
 */
import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  ArrowLeft, Save, Download, Settings, Palette, FileText, Zap,
  Undo, Redo, Grid3X3, ZoomIn, ZoomOut, MousePointer2,
  Move, Square, Circle, Type, Pen, Image, Layers,
  AlignLeft, AlignCenter, AlignRight, RotateCcw, Copy, Trash2, Edit3,
  Brain, Sparkles, Template, Code, Eye
} from 'lucide-react';
import {
  MermaidService,
  MermaidRenderer,
  MermaidThemeManager,
  MermaidExporter,
  setupMermaid
} from '../../services/mermaid';
import {
  GlassPanel,
  GlassButton,
  GlassInput,
  Animation<PERSON><PERSON>ider,
  <PERSON>ade<PERSON>nContainer,
  MermaidDesignTokens
} from '../../components/ui';
import EnhancedSidebar from './EnhancedSidebar';
import SyntaxValidator from './SyntaxValidator';
import VisualElementEditor, { DiagramElement } from './VisualElementEditor';
import { DiagramAIService } from '../../services/ai/DiagramAIService';
import { VisualOverlayService } from '../../services/editor/VisualOverlayService';

interface MermaidGraphicalEditorProps {
  designId?: string;
  showBackButton?: boolean;
  onBack?: () => void;
}

export const MermaidGraphicalEditor: React.FC<MermaidGraphicalEditorProps> = ({
  designId = 'mermaid-editor',
  showBackButton = true,
  onBack
}) => {
  // Core State Management - Text-First Approach
  const [isInitialized, setIsInitialized] = useState(false);
  const [currentTheme, setCurrentTheme] = useState('auditready-dark');
  const [diagramText, setDiagramText] = useState(`flowchart TD
    A[Audit Planning] --> B[Risk Assessment]
    B --> C[Control Testing]
    C --> D[Evidence Collection]
    D --> E[Findings Analysis]
    E --> F[Report Generation]
    F --> G[Management Review]
    G --> H[Action Plan]`);

  // Editor State
  const [isRendering, setIsRendering] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showThemes, setShowThemes] = useState(false);
  const [editorMode, setEditorMode] = useState<'visual' | 'text' | 'split'>('visual');
  const [zoom, setZoom] = useState(1);

  // Text Editor State
  const [canvasHistory, setCanvasHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isValidSyntax, setIsValidSyntax] = useState(true);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [validationSuggestions, setValidationSuggestions] = useState<string[]>([]);

  // Visual Overlay State
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [hoveredNodeId, setHoveredNodeId] = useState<string | null>(null);
  const [showNodeTooltips, setShowNodeTooltips] = useState(true);

  // AI Integration State
  const [isAIGenerating, setIsAIGenerating] = useState(false);
  const [aiPrompt, setAiPrompt] = useState('');

  // Refs
  const canvasRef = useRef<HTMLDivElement>(null);
  const mermaidService = useRef<MermaidService | null>(null);
  const mermaidRenderer = useRef<MermaidRenderer | null>(null);
  const themeManager = useRef<MermaidThemeManager | null>(null);
  const exporter = useRef<MermaidExporter | null>(null);
  const aiService = useRef<DiagramAIService | null>(null);
  const overlayService = useRef<VisualOverlayService | null>(null);

  // Initialize Mermaid services
  useEffect(() => {
    const initializeMermaid = async () => {
      try {
        console.log('🚀 Initializing Mermaid Editor...');

        // Simplified initialization - just get the editor loading first
        setIsInitialized(true);
        console.log('✅ Mermaid Editor initialized successfully (simplified mode)');

        // Add initial diagram to history
        addToHistory(diagramText);

        // Render initial diagram immediately with simple rendering
        setTimeout(() => {
          console.log('🔄 Rendering initial diagram with simple method...');
          renderDiagramSimple();
        }, 100);

        // Try to initialize services in the background
        setTimeout(async () => {
          try {
            // Setup Mermaid with AuditReady configuration
            mermaidService.current = await setupMermaid({
              theme: 'dark',
              themeVariables: {
                primaryColor: '#3b82f6',
                primaryTextColor: '#f8fafc',
                primaryBorderColor: '#1e40af'
              }
            });
            console.log('✅ Mermaid service initialized');

            // Initialize other services with individual error handling
            try {
              mermaidRenderer.current = new MermaidRenderer();
              console.log('✅ MermaidRenderer initialized');
            } catch (error) {
              console.error('❌ Failed to initialize MermaidRenderer:', error);
            }

            try {
              themeManager.current = MermaidThemeManager.getInstance();
              console.log('✅ ThemeManager initialized');
            } catch (error) {
              console.error('❌ Failed to initialize ThemeManager:', error);
            }

            try {
              exporter.current = MermaidExporter.getInstance();
              console.log('✅ Exporter initialized');
            } catch (error) {
              console.error('❌ Failed to initialize Exporter:', error);
            }

            try {
              aiService.current = DiagramAIService.getInstance();
              console.log('✅ AI Service initialized');
            } catch (error) {
              console.error('❌ Failed to initialize AI Service:', error);
            }

            try {
              overlayService.current = VisualOverlayService.getInstance();
              console.log('✅ Overlay Service initialized');
            } catch (error) {
              console.error('❌ Failed to initialize Overlay Service:', error);
            }

            // Initialize overlay service with container
            if (canvasRef.current && overlayService.current) {
              overlayService.current.initialize(canvasRef.current);
            }

            // Set container for renderer (will be set in renderDiagram if not ready)
            if (canvasRef.current && mermaidRenderer.current) {
              try {
                mermaidRenderer.current.setContainer(canvasRef.current);

                // Setup visual editing event listeners
                setupVisualEditingEvents();
              } catch (error) {
                console.log('⚠️ Container setup will be retried in renderDiagram');
              }
            }

            // Set initial theme
            themeManager.current?.setCurrentTheme(currentTheme);

            // Render initial diagram
            console.log('🔄 Rendering initial diagram...');
            renderDiagram();
          } catch (error) {
            console.error('❌ Background service initialization failed:', error);
          }
        }, 500);

      } catch (error) {
        console.error('❌ Failed to initialize Mermaid Editor:', error);

        // Still allow the editor to load even if initialization fails
        setIsInitialized(true);
        console.log('⚠️ Editor loaded with limited functionality');
      }
    };

    initializeMermaid();
  }, []);

  // Simple diagram rendering with proper event handling
  const renderDiagramSimple = async () => {
    if (!canvasRef.current) return;

    try {
      console.log('🔄 Attempting simple Mermaid rendering...');

      // Import mermaid directly for simple rendering
      const mermaid = (await import('mermaid')).default;

      // Initialize with basic config - NO INTERACTION
      mermaid.initialize({
        startOnLoad: false,
        theme: 'dark',
        themeVariables: {
          primaryColor: '#3b82f6',
          primaryTextColor: '#f8fafc',
          primaryBorderColor: '#1e40af'
        },
        securityLevel: 'loose',
        deterministicIds: true
      });

      // Generate unique ID
      const diagramId = `mermaid-simple-${Date.now()}`;

      // Render diagram
      const { svg } = await mermaid.render(diagramId, diagramText);

      // Insert into container
      canvasRef.current.innerHTML = svg;

      // Add simple click handlers for text-first editing
      setTimeout(() => {
        addSimpleClickHandlers();
      }, 100);

      console.log('✅ Simple diagram rendered successfully');
    } catch (error) {
      console.error('❌ Simple rendering failed:', error);

      // Show error message in container
      if (canvasRef.current) {
        canvasRef.current.innerHTML = `
          <div style="
            padding: 24px;
            text-align: center;
            color: #f8fafc;
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
            border-radius: 12px;
            font-family: Inter, sans-serif;
          ">
            <h3>⚠️ Diagram Rendering Error</h3>
            <p>Failed to render diagram: ${(error as Error).message}</p>
            <p style="font-size: 12px; opacity: 0.7;">Check the diagram syntax and try again.</p>
          </div>
        `;
      }
    }
  };

  // Add SAFE click handlers that don't interfere with Mermaid rendering
  const addSimpleClickHandlers = () => {
    if (!canvasRef.current) return;

    const svg = canvasRef.current.querySelector('svg');
    if (!svg) return;

    // Find all nodes and add MINIMAL event handlers
    const nodes = svg.querySelectorAll('.node');
    nodes.forEach((node, index) => {
      // SAFE hover effects - only change cursor and add subtle highlight
      node.addEventListener('mouseenter', () => {
        (node as SVGElement).style.cursor = 'pointer';
        // Add subtle outline instead of filter to avoid rendering issues
        (node as SVGElement).style.outline = '1px solid rgba(59, 130, 246, 0.3)';
      });

      node.addEventListener('mouseleave', () => {
        (node as SVGElement).style.cursor = 'default';
        // Only remove outline if it's not the selected element
        if (!(node as SVGElement).classList.contains('selected')) {
          (node as SVGElement).style.outline = '';
        }
      });

      // Click handler for selection and editing
      node.addEventListener('click', (event) => {
        event.stopPropagation();

        // Check if clicking on text element for inline editing
        if ((event.target as Element).tagName === 'text') {
          handleInlineTextEdit(event.target as SVGTextElement, node as SVGElement, index);
        } else {
          handleNodeSelectionForEditing(node as SVGElement, index);
        }
      });

      // Add drag functionality
      addDragFunctionality(node as SVGElement, index);
    });
  };

  // Add drag functionality to a node - FIXED VERSION
  const addDragFunctionality = (element: SVGElement, index: number) => {
    let isDragging = false;
    let startX = 0;
    let startY = 0;
    let currentX = 0;
    let currentY = 0;

    // Make element visually draggable
    element.style.cursor = 'grab';

    // Parse existing transform to get current position
    const parseTransform = (transform: string): { x: number; y: number } => {
      if (!transform) return { x: 0, y: 0 };
      const match = transform.match(/translate\(([^,]+),\s*([^)]+)\)/);
      if (match) {
        return { x: parseFloat(match[1]) || 0, y: parseFloat(match[2]) || 0 };
      }
      return { x: 0, y: 0 };
    };

    // Get initial position from existing transform
    const initialTransform = element.getAttribute('transform') || '';
    const initialPos = parseTransform(initialTransform);
    currentX = initialPos.x;
    currentY = initialPos.y;

    const handleMouseDown = (event: MouseEvent) => {
      // Only start drag if not clicking on text (to allow text editing)
      if ((event.target as Element).tagName === 'text') return;

      event.preventDefault();
      event.stopPropagation();

      isDragging = true;
      startX = event.clientX;
      startY = event.clientY;

      element.style.cursor = 'grabbing';
      console.log(`🎯 Started dragging node ${index} from position (${currentX}, ${currentY})`);

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    };

    const handleMouseMove = (event: MouseEvent) => {
      if (!isDragging) return;

      event.preventDefault();

      const deltaX = event.clientX - startX;
      const deltaY = event.clientY - startY;

      // Calculate new position
      const newX = currentX + deltaX;
      const newY = currentY + deltaY;

      // Apply transform with absolute positioning - this prevents disappearing
      const newTransform = `translate(${newX}, ${newY})`;
      element.setAttribute('transform', newTransform);
    };

    const handleMouseUp = (event: MouseEvent) => {
      if (!isDragging) return;

      isDragging = false;
      element.style.cursor = 'grab';

      const deltaX = event.clientX - startX;
      const deltaY = event.clientY - startY;

      // Update current position
      currentX += deltaX;
      currentY += deltaY;

      console.log(`🎯 Finished dragging node ${index}, final position (${currentX}, ${currentY})`);

      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      // Apply final transform
      const finalTransform = `translate(${currentX}, ${currentY})`;
      element.setAttribute('transform', finalTransform);

      console.log('✅ Node drag completed - position updated visually');
    };

    element.addEventListener('mousedown', handleMouseDown);
  };

  // Inline text editing handler - MERMAID APPROACH
  const handleInlineTextEdit = (textElement: SVGTextElement, nodeElement: SVGElement, index: number) => {
    console.log('🎯 Starting inline text edit for node:', index);

    const currentText = textElement.textContent || '';
    const rect = textElement.getBoundingClientRect();

    // Create input element for editing
    const input = document.createElement('input');
    input.type = 'text';
    input.value = currentText;
    input.style.cssText = `
      position: fixed;
      left: ${rect.left}px;
      top: ${rect.top}px;
      width: ${Math.max(rect.width, 100)}px;
      height: ${rect.height}px;
      font-size: ${window.getComputedStyle(textElement).fontSize};
      font-family: ${window.getComputedStyle(textElement).fontFamily};
      background: white;
      border: 2px solid #3b82f6;
      border-radius: 4px;
      padding: 2px 6px;
      z-index: 10000;
      outline: none;
    `;

    // Hide original text temporarily
    textElement.style.opacity = '0';

    // Add input to document
    document.body.appendChild(input);
    input.focus();
    input.select();

    const finishEdit = () => {
      const newText = input.value.trim();
      if (newText && newText !== currentText) {
        // Update the text element
        textElement.textContent = newText;

        // Extract node ID from the SVG element
        const nodeId = extractNodeIdFromElement(nodeElement);
        if (nodeId) {
          // Update the diagram text
          const updatedText = updateNodeTextInDiagram(diagramText, nodeId, newText);
          console.log('📝 Updated diagram text after inline edit:', updatedText);

          setDiagramText(updatedText);
          addToHistory(updatedText);

          // Re-render diagram with updated text
          setTimeout(() => {
            console.log('🔄 Triggering diagram re-render after inline edit...');
            renderDiagramSimple();
          }, 200);
        }
      }

      // Cleanup
      textElement.style.opacity = '1';
      document.body.removeChild(input);
    };

    // Handle input events
    input.addEventListener('blur', finishEdit);
    input.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        finishEdit();
      } else if (e.key === 'Escape') {
        e.preventDefault();
        textElement.style.opacity = '1';
        document.body.removeChild(input);
      }
    });
  };

  // Extract node ID from SVG element
  const extractNodeIdFromElement = (element: SVGElement): string | null => {
    // Try to find node ID from various attributes
    const id = element.id || element.getAttribute('data-id') || element.getAttribute('data-node-id');
    if (id) return id;

    // Try to extract from class names
    const classList = Array.from(element.classList);
    for (const className of classList) {
      if (className.startsWith('node-') || className.match(/^[A-Za-z]\w*$/)) {
        return className.replace('node-', '');
      }
    }

    // Try to find from parent elements
    let parent = element.parentElement;
    while (parent && parent.tagName !== 'svg') {
      const parentId = parent.id || parent.getAttribute('data-id');
      if (parentId) return parentId;
      parent = parent.parentElement;
    }

    return null;
  };

  // Node selection handler that makes nodes editable
  const handleNodeSelectionForEditing = (node: SVGElement, index: number) => {
    console.log('🎯 Node selected for editing:', index);

    // Clear previous selections
    const allNodes = canvasRef.current?.querySelectorAll('.node');
    allNodes?.forEach(n => {
      (n as SVGElement).classList.remove('selected');
      (n as SVGElement).style.outline = '';
    });

    // Mark as selected and highlight
    node.classList.add('selected');
    node.style.outline = '2px solid #3b82f6';
    node.style.outlineOffset = '2px';

    // Extract node text
    const textElement = node.querySelector('text');
    const currentText = textElement?.textContent || '';

    // Extract node ID from diagram text
    const nodeId = extractNodeIdFromText(currentText, index);

    // Set selected element for sidebar editing
    const element: DiagramElement = {
      id: nodeId,
      type: 'node',
      text: currentText,
      svgElement: node,
      shape: 'rectangle', // Default shape
      style: {
        fill: '#3b82f6',
        stroke: '#1e40af',
        strokeWidth: 2,
        fontSize: 14,
        fontWeight: '500'
      }
    };

    setSelectedElement(element);

    // Switch to Visual tab to show editing options
    // This will make the node editable in the sidebar
    console.log('✅ Node ready for editing in Visual tab:', nodeId, currentText);
  };

  // Extract node ID from diagram text based on content
  const extractNodeIdFromText = (text: string, fallbackIndex: number): string => {
    const lines = diagramText.split('\n');

    for (const line of lines) {
      // Match patterns like: A[Text], B(Text), C{Text}
      const nodeMatch = line.match(/(\w+)[\[\(\{]([^\]\)\}]+)[\]\)\}]/);
      if (nodeMatch && nodeMatch[2].trim() === text.trim()) {
        return nodeMatch[1];
      }
    }

    // Fallback to alphabet-based ID
    return String.fromCharCode(65 + fallbackIndex);
  };

  // Render diagram
  const renderDiagram = async () => {
    if (!diagramText.trim()) {
      console.log('⚠️ No diagram text to render');
      return;
    }

    // If renderer is not ready, try simple mermaid rendering
    if (!mermaidRenderer.current) {
      console.log('⚠️ Renderer not ready, attempting simple rendering...');
      await renderDiagramSimple();
      return;
    }

    // Check if container is available
    if (!canvasRef.current) {
      console.log('⚠️ Canvas container not ready, retrying...');
      setTimeout(() => renderDiagram(), 100);
      return;
    }

    // Ensure container is set
    if (!mermaidRenderer.current.hasContainer()) {
      mermaidRenderer.current.setContainer(canvasRef.current);
    }

    setIsRendering(true);
    try {
      await mermaidRenderer.current.renderToContainer(diagramText);
      console.log('✅ Diagram rendered successfully');

      // Simple event handling - no complex overlays
      setTimeout(() => {
        addSimpleClickHandlers();
      }, 100);
    } catch (error) {
      console.error('❌ Failed to render diagram:', error);
    } finally {
      setIsRendering(false);
    }
  };

  // Handle theme change
  const handleThemeChange = async (themeName: string) => {
    if (!themeManager.current || !mermaidService.current) return;

    try {
      themeManager.current.setCurrentTheme(themeName);
      const themeConfig = themeManager.current.generateMermaidThemeConfig();
      mermaidService.current.updateConfig(themeConfig);
      setCurrentTheme(themeName);
      await renderDiagram();
      console.log(`✅ Theme changed to: ${themeName}`);
    } catch (error) {
      console.error('❌ Failed to change theme:', error);
    }
  };

  // Handle export
  const handleExport = async (format: 'svg' | 'png' | 'pdf') => {
    if (!exporter.current || !mermaidRenderer.current) return;

    try {
      const svg = mermaidRenderer.current.exportAsSVG();
      if (!svg) return;

      let blob: Blob;
      let filename: string;

      switch (format) {
        case 'svg':
          blob = await exporter.current.exportAsSVG(svg);
          filename = `diagram-${Date.now()}.svg`;
          break;
        case 'png':
          blob = await exporter.current.exportAsPNG(svg, { scale: 2 });
          filename = `diagram-${Date.now()}.png`;
          break;
        case 'pdf':
          blob = await exporter.current.exportAsPDF(svg);
          filename = `diagram-${Date.now()}.pdf`;
          break;
        default:
          return;
      }

      exporter.current.downloadBlob(blob, filename);
      console.log(`✅ Exported as ${format.toUpperCase()}`);
    } catch (error) {
      console.error(`❌ Failed to export as ${format}:`, error);
    }
  };

  // Canvas control handlers
  const handleZoomIn = () => {
    const newZoom = Math.min(zoom + 0.1, 3);
    setZoom(newZoom);
    if (canvasRef.current) {
      canvasRef.current.style.transform = `scale(${newZoom})`;
    }
  };

  const handleZoomOut = () => {
    const newZoom = Math.max(zoom - 0.1, 0.2);
    setZoom(newZoom);
    if (canvasRef.current) {
      canvasRef.current.style.transform = `scale(${newZoom})`;
    }
  };

  const handleResetZoom = () => {
    setZoom(1);
    if (canvasRef.current) {
      canvasRef.current.style.transform = 'scale(1)';
    }
  };

  const handleToggleGrid = () => {
    setShowGrid(!showGrid);
  };

  const handleToggleSnap = () => {
    setSnapToGrid(!snapToGrid);
  };







  // Missing state variables
  const [selectedTool, setSelectedTool] = useState('select');
  const [showGrid, setShowGrid] = useState(true);
  const [snapToGrid, setSnapToGrid] = useState(true);
  const [selectedElement, setSelectedElement] = useState<DiagramElement | null>(null);
  const [isVisualEditingMode, setIsVisualEditingMode] = useState(false);

  // History handlers
  const handleUndo = () => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      setDiagramText(canvasHistory[newIndex]);
    }
  };

  const handleRedo = () => {
    if (historyIndex < canvasHistory.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      setDiagramText(canvasHistory[newIndex]);
    }
  };

  const addToHistory = (text: string) => {
    const newHistory = canvasHistory.slice(0, historyIndex + 1);
    newHistory.push(text);
    setCanvasHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };

  // Handle validation changes
  const handleValidationChange = (isValid: boolean, errors?: string[], suggestions?: string[]) => {
    setIsValidSyntax(isValid);
    setValidationErrors(errors || []);
    setValidationSuggestions(suggestions || []);
  };

  // Setup visual editing event listeners
  const setupVisualEditingEvents = () => {
    if (!canvasRef.current) {
      console.warn('⚠️ Cannot setup event listeners - no canvas ref');
      return;
    }

    console.log('🎯 Setting up visual editing event listeners on:', canvasRef.current);

    // Create event handlers with proper binding
    const handleNodeSelected = (event: any) => {
      console.log('🎯 Node selected event received:', event.detail);
      const { node, index } = event.detail;
      handleElementSelection(node, index, 'node');
    };

    const handleEdgeSelected = (event: any) => {
      console.log('🎯 Edge selected event received:', event.detail);
      const { edge, index } = event.detail;
      handleElementSelection(edge, index, 'edge');
    };

    const handleCanvasClicked = () => {
      console.log('🎯 Canvas clicked - clearing selection');
      setSelectedElement(null);
    };

    const handleCanvasClick = (event: MouseEvent) => {
      // Only handle clicks on the canvas itself or SVG background, not on nodes
      const target = event.target as Element;
      const isCanvasClick = target === canvasRef.current ||
                           (target.tagName === 'svg' && !target.closest('.node, .edge'));

      if (selectedTool === 'shape' && isCanvasClick) {
        console.log('🎯 Canvas clicked for shape addition');
        handleShapeAdd('rectangle', 'New Shape');
      }
    };

    // Remove existing listeners to prevent duplicates
    canvasRef.current.removeEventListener('mermaid:nodeSelected', handleNodeSelected);
    canvasRef.current.removeEventListener('mermaid:edgeSelected', handleEdgeSelected);
    canvasRef.current.removeEventListener('mermaid:canvasClicked', handleCanvasClicked);
    canvasRef.current.removeEventListener('click', handleCanvasClick);

    // Add event listeners
    canvasRef.current.addEventListener('mermaid:nodeSelected', handleNodeSelected);
    canvasRef.current.addEventListener('mermaid:edgeSelected', handleEdgeSelected);
    canvasRef.current.addEventListener('mermaid:canvasClicked', handleCanvasClicked);
    canvasRef.current.addEventListener('click', handleCanvasClick);

    console.log('✅ Visual editing event listeners set up successfully');
  };

  // Canvas click handler for clearing selection
  const handleCanvasClick = () => {
    console.log('🎯 Canvas clicked - clearing selection');
    setSelectedElement(null);

    // Clear all node outlines
    const allNodes = canvasRef.current?.querySelectorAll('.node');
    allNodes?.forEach(n => {
      (n as SVGElement).style.outline = '';
    });
  };

  // Generic event listener handler for cleanup
  const handleEventListener = (event: any) => {
    // This is used for cleanup only
  };

  // Handle element selection
  const handleElementSelection = (svgElement: SVGElement, index: number, type: 'node' | 'edge') => {
    const textElement = svgElement.querySelector('text');
    const text = textElement?.textContent || '';

    // Try to extract actual Mermaid node ID from the SVG
    const nodeId = extractMermaidNodeId(svgElement, text, index);

    const element: DiagramElement = {
      id: nodeId,
      type,
      text,
      svgElement,
      style: extractElementStyle(svgElement)
    };

    setSelectedElement(element);
    console.log('🎯 Element selected:', nodeId, 'Text:', text);
  };

  // Extract Mermaid node ID from SVG element
  const extractMermaidNodeId = (svgElement: SVGElement, text: string, index: number): string => {
    // Try to find the node ID from the diagram text using ElementParser
    try {
      const ElementParser = require('../../services/editor/ElementParser').ElementParser;
      const parser = ElementParser.getInstance();
      const parsed = parser.parseDiagram(diagramText);

      // Find node by text content
      const matchingNode = parsed.ast.nodes.find(node =>
        node.text.trim().toLowerCase() === text.trim().toLowerCase()
      );

      if (matchingNode) {
        return matchingNode.id;
      }
    } catch (error) {
      console.warn('Failed to parse diagram for node ID extraction:', error);
    }

    // Fallback: try manual parsing
    const lines = diagramText.split('\n');
    for (const line of lines) {
      const nodeMatch = line.match(/(\w+)[\[\(\{]([^\]\)\}]+)[\]\)\}]/);
      if (nodeMatch && nodeMatch[2].trim() === text.trim()) {
        return nodeMatch[1];
      }
    }

    // Final fallback to generated ID
    return `node${index + 1}`;
  };

  // Extract style from SVG element
  const extractElementStyle = (element: SVGElement) => {
    const computedStyle = window.getComputedStyle(element);
    return {
      fill: computedStyle.fill !== 'none' ? computedStyle.fill : '#3b82f6',
      stroke: computedStyle.stroke !== 'none' ? computedStyle.stroke : '#1e40af',
      strokeWidth: computedStyle.strokeWidth ? parseFloat(computedStyle.strokeWidth) : 2,
      fontSize: computedStyle.fontSize ? parseFloat(computedStyle.fontSize) : 14,
      fontWeight: computedStyle.fontWeight || '500'
    };
  };

  // Handle element updates - TEXT-FIRST APPROACH
  const handleElementUpdate = (element: DiagramElement) => {
    console.log('🔄 Updating element:', element.id, 'New text:', element.text);

    // Update diagram text if text changed
    if (element.text !== selectedElement?.text) {
      const updatedText = updateNodeTextInDiagram(diagramText, element.id, element.text);
      console.log('📝 Updated diagram text:', updatedText);

      setDiagramText(updatedText);
      addToHistory(updatedText);

      // Update selected element to reflect changes
      setSelectedElement({ ...element });

      // Re-render diagram with updated text
      setTimeout(() => {
        console.log('🔄 Triggering diagram re-render after text update...');
        renderDiagramSimple(); // Use simple rendering for consistency
      }, 200);
    }
  };

  // Update node text in diagram - proper text manipulation
  const updateNodeTextInDiagram = (diagramText: string, nodeId: string, newText: string): string => {
    const lines = diagramText.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Match node definitions: A[text], B(text), C{text}
      const nodeMatch = line.match(/(\w+)([\[\(\{])([^\]\)\}]+)([\]\)\}])/);
      if (nodeMatch && nodeMatch[1] === nodeId) {
        // Replace the text while keeping the brackets
        lines[i] = line.replace(
          /(\w+)([\[\(\{])([^\]\)\}]+)([\]\)\}])/,
          `$1$2${newText}$4`
        );
        break;
      }
    }

    return lines.join('\n');
  };



  // Handle shape addition - SIMPLE TEXT-FIRST APPROACH
  const handleShapeAdd = async (shapeType: DiagramElement['shape'], text?: string) => {
    try {
      console.log('🔧 Adding shape:', shapeType, 'with text:', text);

      const newNodeText = text || 'New Node';
      const newNodeId = generateNextNodeId(diagramText);

      // Determine bracket type based on shape
      let brackets = ['[', ']']; // rectangle
      if (shapeType === 'circle') brackets = ['((', '))'];
      if (shapeType === 'diamond') brackets = ['{', '}'];
      if (shapeType === 'hexagon') brackets = ['{{', '}}'];

      // Add new node to diagram text
      const lines = diagramText.split('\n');
      const newNodeLine = `    ${newNodeId}${brackets[0]}${newNodeText}${brackets[1]}`;

      // Find a good place to insert the new node
      let insertIndex = lines.length;
      for (let i = 0; i < lines.length; i++) {
        if (lines[i].trim() === '' || lines[i].includes('-->')) {
          insertIndex = i;
          break;
        }
      }

      lines.splice(insertIndex, 0, newNodeLine);
      const updatedText = lines.join('\n');

      console.log('✅ Shape added successfully:', newNodeId);
      console.log('📝 Updated diagram text:', updatedText);

      // Update state and trigger re-render
      setDiagramText(updatedText);
      addToHistory(updatedText);

      // Force re-render after state update
      setTimeout(() => {
        console.log('🔄 Triggering diagram re-render...');
        renderDiagramSimple();
      }, 200);

    } catch (error) {
      console.error('❌ Failed to add shape:', error);
      alert(`Failed to add shape: ${(error as Error).message}`);
    }
  };

  // Generate next available node ID
  const generateNextNodeId = (diagramText: string): string => {
    const existingIds = new Set<string>();
    const lines = diagramText.split('\n');

    // Extract existing node IDs
    for (const line of lines) {
      const nodeMatch = line.match(/(\w+)[\[\(\{]/);
      if (nodeMatch) {
        existingIds.add(nodeMatch[1]);
      }
    }

    // Generate next available ID (A, B, C, ...)
    for (let i = 0; i < 26; i++) {
      const id = String.fromCharCode(65 + i); // A, B, C, ...
      if (!existingIds.has(id)) {
        return id;
      }
    }

    // Fallback to numbered IDs
    for (let i = 1; i < 100; i++) {
      const id = `N${i}`;
      if (!existingIds.has(id)) {
        return id;
      }
    }

    return 'NEW';
  };

  // Handle element deletion
  const handleElementDelete = (elementId: string) => {
    if (mermaidRenderer.current) {
      mermaidRenderer.current.deleteElement(elementId);
    }
    setSelectedElement(null);
  };

  // Handle element duplication
  const handleElementDuplicate = (element: DiagramElement) => {
    if (mermaidRenderer.current) {
      const duplicated = mermaidRenderer.current.duplicateElement(element.id);
      if (duplicated) {
        setSelectedElement(duplicated);
      }
    }
  };

  // AI-powered diagram generation
  const handleAIGeneration = async (prompt: string) => {
    if (!aiService.current) return;

    setIsAIGenerating(true);
    try {
      console.log('🤖 Generating diagram from AI prompt:', prompt);

      const response = await aiService.current.generateDiagram({
        prompt,
        diagramType: 'flowchart',
        complexity: 'medium',
        includeParallelPaths: true,
        industry: 'audit'
      });

      console.log('✅ AI diagram generated successfully');

      setDiagramText(response.mermaidCode);
      addToHistory(response.mermaidCode);

      // Clear the prompt
      setAiPrompt('');

      // Render the new diagram
      setTimeout(() => renderDiagram(), 200);

    } catch (error) {
      console.error('❌ AI generation failed:', error);
      alert(`AI generation failed: ${(error as Error).message}`);
    } finally {
      setIsAIGenerating(false);
    }
  };

  // Generate process flow with AI
  const handleProcessFlowGeneration = async (processName: string, steps: string[]) => {
    if (!aiService.current) return;

    setIsAIGenerating(true);
    try {
      console.log('🔄 Generating process flow:', processName);

      const response = await aiService.current.generateProcessFlow({
        processName,
        steps,
        includeDecisionPoints: true,
        includeParallelTasks: true,
        addErrorHandling: true
      });

      console.log('✅ Process flow generated successfully');

      setDiagramText(response.mermaidCode);
      addToHistory(response.mermaidCode);

      // Render the new diagram
      setTimeout(() => renderDiagram(), 200);

    } catch (error) {
      console.error('❌ Process flow generation failed:', error);
      alert(`Process flow generation failed: ${(error as Error).message}`);
    } finally {
      setIsAIGenerating(false);
    }
  };

  // Update diagram text with element changes using ElementParser
  const updateDiagramTextWithElement = (text: string, element: DiagramElement): string => {
    // Fallback to manual text replacement for now
    const lines = text.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Look for node definitions and update text
      const nodeMatch = line.match(/(\w+)[\[\(\{]([^\]\)\}]+)[\]\)\}]/);
      if (nodeMatch && nodeMatch[1] === element.id) {
        const brackets = line.includes('[') ? ['[', ']'] :
                        line.includes('(') ? ['(', ')'] :
                        line.includes('{') ? ['{', '}'] : ['[', ']'];
        lines[i] = line.replace(/[\[\(\{][^\]\)\}]+[\]\)\}]/, `${brackets[0]}${element.text}${brackets[1]}`);
        console.log('✅ Updated node text:', element.id, 'to:', element.text);
        break;
      }
    }

    return lines.join('\n');
  };

  // Toggle visual editing mode
  const toggleVisualEditingMode = () => {
    const newMode = !isVisualEditingMode;
    setIsVisualEditingMode(newMode);

    if (mermaidRenderer.current) {
      if (newMode) {
        mermaidRenderer.current.enableVisualEditingMode();
      } else {
        mermaidRenderer.current.disableVisualEditingMode();
      }
    }

    // Re-render to apply changes
    renderDiagram();
  };

  // Handle back navigation
  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      // Default back behavior
      try {
        const referrer = document.referrer;
        if (referrer && referrer.includes('/app/documents')) {
          window.history.back();
        } else {
          window.location.href = '/app';
        }
      } catch (e) {
        window.location.href = '/app';
      }
    }
  };

  // Get available themes
  const availableThemes = themeManager.current?.getAllThemes() || [];

  // Editor styles
  const editorStyles: React.CSSProperties = {
    width: '100vw',
    height: '100vh',
    background: MermaidDesignTokens.colors.secondary.gradient,
    display: 'flex',
    flexDirection: 'column',
    fontFamily: MermaidDesignTokens.typography.fontFamily.sans,
    color: MermaidDesignTokens.colors.text.primary,
    overflow: 'hidden'
  };

  const headerStyles: React.CSSProperties = {
    height: '4rem',
    background: MermaidDesignTokens.colors.primary.gradient,
    backdropFilter: MermaidDesignTokens.backdropBlur.lg,
    borderBottom: `1px solid ${MermaidDesignTokens.colors.glass.border}`,
    padding: `0 ${MermaidDesignTokens.spacing[6]}`,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    boxShadow: MermaidDesignTokens.shadows.glass.lg
  };

  const mainStyles: React.CSSProperties = {
    flex: 1,
    display: 'flex',
    overflow: 'hidden'
  };



  const canvasStyles: React.CSSProperties = {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    background: MermaidDesignTokens.colors.glass.primary,
    margin: MermaidDesignTokens.spacing[4],
    borderRadius: MermaidDesignTokens.borderRadius['2xl'],
    border: `1px solid ${MermaidDesignTokens.colors.glass.border}`,
    backdropFilter: MermaidDesignTokens.backdropBlur.lg,
    overflow: 'hidden'
  };

  if (!isInitialized) {
    return (
      <div style={editorStyles}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          fontSize: MermaidDesignTokens.typography.fontSize.lg,
          color: MermaidDesignTokens.colors.text.secondary
        }}>
          🚀 Initializing Mermaid Editor...
        </div>
      </div>
    );
  }

  return (
    <AnimationProvider>
      <div style={editorStyles}>
        {/* Header */}
        <FadeInContainer style={headerStyles}>
          <div style={{ display: 'flex', alignItems: 'center', gap: MermaidDesignTokens.spacing[4] }}>
            {showBackButton && (
              <GlassButton
                variant="ghost"
                size="sm"
                icon={<ArrowLeft size={16} />}
                onClick={handleBack}
              >
                Back
              </GlassButton>
            )}
            <h1 style={{
              fontSize: MermaidDesignTokens.typography.fontSize.xl,
              fontWeight: MermaidDesignTokens.typography.fontWeight.bold,
              margin: 0
            }}>
              AuditReady Mermaid Editor
            </h1>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: MermaidDesignTokens.spacing[2] }}>
            {/* Undo/Redo Controls */}
            <GlassButton
              variant="ghost"
              size="sm"
              icon={<Undo size={16} />}
              onClick={handleUndo}
              disabled={historyIndex <= 0}
              title="Undo (Ctrl+Z)"
            />
            <GlassButton
              variant="ghost"
              size="sm"
              icon={<Redo size={16} />}
              onClick={handleRedo}
              disabled={historyIndex >= canvasHistory.length - 1}
              title="Redo (Ctrl+Y)"
            />

            <div style={{
              width: '1px',
              height: '24px',
              background: MermaidDesignTokens.colors.glass.border,
              margin: `0 ${MermaidDesignTokens.spacing[2]}`
            }} />

            {/* Editor Mode Switcher */}
            <div className="flex items-center bg-black/20 rounded-lg p-1">
              <GlassButton
                variant={editorMode === 'visual' ? 'primary' : 'ghost'}
                size="sm"
                icon={<Eye size={16} />}
                onClick={() => setEditorMode('visual')}
                title="Visual Mode"
              />
              <GlassButton
                variant={editorMode === 'text' ? 'primary' : 'ghost'}
                size="sm"
                icon={<Code size={16} />}
                onClick={() => setEditorMode('text')}
                title="Text Mode"
              />
              <GlassButton
                variant={editorMode === 'split' ? 'primary' : 'ghost'}
                size="sm"
                icon={<Layers size={16} />}
                onClick={() => setEditorMode('split')}
                title="Split Mode"
              />
            </div>

            <div style={{
              width: '1px',
              height: '24px',
              background: MermaidDesignTokens.colors.glass.border,
              margin: `0 ${MermaidDesignTokens.spacing[2]}`
            }} />

            <GlassButton
              variant="secondary"
              size="sm"
              icon={<Palette size={16} />}
              onClick={() => setShowThemes(!showThemes)}
            >
              Themes
            </GlassButton>
            <GlassButton
              variant="secondary"
              size="sm"
              icon={<Settings size={16} />}
              onClick={() => setShowSettings(!showSettings)}
            >
              Settings
            </GlassButton>
            <div className="flex items-center gap-2">
              <GlassInput
                placeholder="Describe your diagram..."
                value={aiPrompt}
                onChange={(e) => setAiPrompt(e.target.value)}
                className="w-64"
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && aiPrompt.trim()) {
                    handleAIGeneration(aiPrompt.trim());
                  }
                }}
              />
              <GlassButton
                variant="primary"
                size="sm"
                icon={<Brain size={16} />}
                onClick={() => aiPrompt.trim() && handleAIGeneration(aiPrompt.trim())}
                disabled={isAIGenerating || !aiPrompt.trim()}
                glow
              >
                {isAIGenerating ? 'Generating...' : 'AI Create'}
              </GlassButton>
            </div>

            <GlassButton
              variant="secondary"
              size="sm"
              icon={<Sparkles size={16} />}
              onClick={() => handleProcessFlowGeneration('Sample Process', [
                'Initialize System',
                'Validate Input',
                'Process Data',
                'Generate Output',
                'Complete'
              ])}
              disabled={isAIGenerating}
            >
              Quick Flow
            </GlassButton>
            <GlassButton
              variant="primary"
              size="sm"
              icon={<Download size={16} />}
              onClick={() => handleExport('png')}
              glow
            >
              Export
            </GlassButton>
          </div>
        </FadeInContainer>

        {/* Main Content */}
        <div style={mainStyles}>
          {/* Enhanced Sidebar */}
          <EnhancedSidebar
            diagramText={diagramText}
            onDiagramTextChange={(text) => {
              setDiagramText(text);
              setTimeout(() => addToHistory(text), 1000);
            }}
            onRenderDiagram={renderDiagram}
            isRendering={isRendering}
            showThemes={showThemes}
            onToggleThemes={() => setShowThemes(!showThemes)}
            availableThemes={availableThemes}
            currentTheme={currentTheme}
            onThemeChange={handleThemeChange}
            onExport={handleExport}
            selectedElement={selectedElement}
            onElementUpdate={handleElementUpdate}
            onElementDelete={handleElementDelete}
            onElementDuplicate={handleElementDuplicate}
            isVisualEditingMode={isVisualEditingMode}
            onShapeAdd={handleShapeAdd}
          />

          {/* Canvas Area */}
          <FadeInContainer delay={200} style={canvasStyles}>
            {/* Floating Canvas Controls - Upper Center */}
            <div style={{
              position: 'absolute',
              top: MermaidDesignTokens.spacing[4],
              left: '50%',
              transform: 'translateX(-50%)',
              zIndex: 10,
              display: 'flex',
              gap: MermaidDesignTokens.spacing[2]
            }}>
              {/* Zoom Controls */}
              <GlassPanel variant="elevated" padding="2" style={{ display: 'flex', gap: MermaidDesignTokens.spacing[1] }}>
                <GlassButton
                  variant="ghost"
                  size="sm"
                  icon={<ZoomOut size={16} />}
                  onClick={handleZoomOut}
                  title="Zoom Out"
                  disabled={zoom <= 0.2}
                />
                <span style={{
                  fontSize: MermaidDesignTokens.typography.fontSize.sm,
                  color: MermaidDesignTokens.colors.text.secondary,
                  padding: `0 ${MermaidDesignTokens.spacing[2]}`,
                  display: 'flex',
                  alignItems: 'center',
                  minWidth: '60px',
                  justifyContent: 'center'
                }}>
                  {Math.round(zoom * 100)}%
                </span>
                <GlassButton
                  variant="ghost"
                  size="sm"
                  icon={<ZoomIn size={16} />}
                  onClick={handleZoomIn}
                  title="Zoom In"
                  disabled={zoom >= 3}
                />
                <GlassButton
                  variant="ghost"
                  size="sm"
                  onClick={handleResetZoom}
                  title="Reset Zoom (100%)"
                  style={{ fontSize: MermaidDesignTokens.typography.fontSize.xs }}
                >
                  1:1
                </GlassButton>
              </GlassPanel>

              {/* Grid Controls */}
              <GlassPanel variant="elevated" padding="2" style={{ display: 'flex', gap: MermaidDesignTokens.spacing[1] }}>
                <GlassButton
                  variant={showGrid ? "primary" : "ghost"}
                  size="sm"
                  icon={<Grid3X3 size={16} />}
                  onClick={handleToggleGrid}
                  title={showGrid ? "Hide Grid" : "Show Grid"}
                />
                <GlassButton
                  variant={snapToGrid ? "primary" : "ghost"}
                  size="sm"
                  icon={<MousePointer2 size={16} />}
                  onClick={handleToggleSnap}
                  title={snapToGrid ? "Disable Snap" : "Enable Snap"}
                />
              </GlassPanel>
            </div>

            {/* Floating Tool Palette - Left Side */}
            <div style={{
              position: 'absolute',
              top: MermaidDesignTokens.spacing[4],
              left: MermaidDesignTokens.spacing[4],
              zIndex: 10
            }}>
              <GlassPanel variant="elevated" padding="2" style={{
                display: 'flex',
                flexDirection: 'column',
                gap: MermaidDesignTokens.spacing[1],
                minWidth: '48px'
              }}>
                <GlassButton
                  variant={selectedTool === 'select' ? "primary" : "ghost"}
                  size="sm"
                  icon={<MousePointer2 size={16} />}
                  onClick={() => setSelectedTool('select')}
                  title="Select Tool"
                />
                <GlassButton
                  variant={selectedTool === 'move' ? "primary" : "ghost"}
                  size="sm"
                  icon={<Move size={16} />}
                  onClick={() => setSelectedTool('move')}
                  title="Move Tool"
                />
                <div style={{
                  width: '100%',
                  height: '1px',
                  background: MermaidDesignTokens.colors.glass.border,
                  margin: `${MermaidDesignTokens.spacing[1]} 0`
                }} />
                <GlassButton
                  variant={selectedTool === 'text' ? "primary" : "ghost"}
                  size="sm"
                  icon={<Type size={16} />}
                  onClick={() => setSelectedTool('text')}
                  title="Text Tool"
                />
                <GlassButton
                  variant={selectedTool === 'shape' ? "primary" : "ghost"}
                  size="sm"
                  icon={<Square size={16} />}
                  onClick={() => {
                    setSelectedTool('shape');
                    // Immediately add a rectangle shape when tool is selected
                    handleShapeAdd('rectangle', 'New Shape');
                  }}
                  title="Add Rectangle Shape"
                />
                <GlassButton
                  variant={selectedTool === 'draw' ? "primary" : "ghost"}
                  size="sm"
                  icon={<Pen size={16} />}
                  onClick={() => setSelectedTool('draw')}
                  title="Draw Tool"
                />
                <div style={{
                  width: '100%',
                  height: '1px',
                  background: MermaidDesignTokens.colors.glass.border,
                  margin: `${MermaidDesignTokens.spacing[1]} 0`
                }} />
                <GlassButton
                  variant={isVisualEditingMode ? "primary" : "ghost"}
                  size="sm"
                  icon={<Edit3 size={16} />}
                  onClick={toggleVisualEditingMode}
                  title={isVisualEditingMode ? "Disable Visual Editing" : "Enable Visual Editing"}
                />
              </GlassPanel>
            </div>

            {/* Canvas Content */}
            <div
              ref={canvasRef}
              style={{
                flex: 1,
                padding: MermaidDesignTokens.spacing[6],
                overflow: 'auto',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative',
                background: showGrid ?
                  `radial-gradient(circle, ${MermaidDesignTokens.colors.glass.border} 1px, transparent 1px)` :
                  'transparent',
                backgroundSize: showGrid ? '20px 20px' : 'auto',
                transition: `background ${MermaidDesignTokens.animation.duration[300]} ${MermaidDesignTokens.animation.easing.inOut}`
              }}
            />
          </FadeInContainer>
        </div>
      </div>
    </AnimationProvider>
  );
};

export default MermaidGraphicalEditor;

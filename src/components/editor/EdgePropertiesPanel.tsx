/**
 * Edge Properties Panel - Visual editing for connections, arrows, lines
 * Provides comprehensive visual editing capabilities for edges
 */

import React, { useState, useCallback } from 'react';
import {
  ArrowR<PERSON>, ArrowLeft, ArrowUp, ArrowDown,
  Minus, Plus, Palette, Type, Trash2, Copy,
  <PERSON>otateCcw, Lock, Unlock
} from 'lucide-react';
import {
  GlassPanel,
  GlassButton,
  GlassInput,
  MermaidDesignTokens
} from '../ui';

interface EdgePropertiesProps {
  selectedEdge: any;
  onEdgeUpdate: (edgeId: string, updates: any) => void;
  onEdgeDelete: (edgeId: string) => void;
  onEdgeDuplicate: (edgeId: string) => void;
  isVisible: boolean;
  onClose: () => void;
}

const ARROW_TYPES = [
  { value: 'arrow', label: 'Arrow', icon: ArrowRight, syntax: '-->' },
  { value: 'line', label: 'Line', icon: Minus, syntax: '---' },
  { value: 'dotted', label: 'Dotted', icon: Minus, syntax: '-.-' },
  { value: 'thick', label: 'Thick', icon: Minus, syntax: '===' },
  { value: 'double', label: 'Double Arrow', icon: ArrowRight, syntax: '<=>' }
];

const LINE_STYLES = [
  { value: 'straight', label: 'Straight' },
  { value: 'curved', label: 'Curved' },
  { value: 'step', label: 'Step' }
];

const COLOR_PRESETS = [
  { name: 'Blue', color: '#2563eb' },
  { name: 'Green', color: '#10b981' },
  { name: 'Orange', color: '#f59e0b' },
  { name: 'Red', color: '#ef4444' },
  { name: 'Purple', color: '#8b5cf6' },
  { name: 'Gray', color: '#6b7280' }
];

export const EdgePropertiesPanel: React.FC<EdgePropertiesProps> = ({
  selectedEdge,
  onEdgeUpdate,
  onEdgeDelete,
  onEdgeDuplicate,
  isVisible,
  onClose
}) => {
  const [localLabel, setLocalLabel] = useState(selectedEdge?.label || '');
  const [localColor, setLocalColor] = useState(selectedEdge?.style?.stroke || '#6b7280');
  const [localWidth, setLocalWidth] = useState(selectedEdge?.style?.strokeWidth || 2);
  const [localArrowType, setLocalArrowType] = useState(selectedEdge?.arrowType || 'arrow');
  const [localLineStyle, setLocalLineStyle] = useState(selectedEdge?.lineStyle || 'straight');
  const [isLocked, setIsLocked] = useState(selectedEdge?.locked || false);

  const handleUpdate = useCallback((field: string, value: any) => {
    if (!selectedEdge) return;
    
    const updates = { [field]: value };
    onEdgeUpdate(selectedEdge.id, updates);
    
    // Update local state
    switch (field) {
      case 'label':
        setLocalLabel(value);
        break;
      case 'color':
        setLocalColor(value);
        break;
      case 'width':
        setLocalWidth(value);
        break;
      case 'arrowType':
        setLocalArrowType(value);
        break;
      case 'lineStyle':
        setLocalLineStyle(value);
        break;
      case 'locked':
        setIsLocked(value);
        break;
    }
  }, [selectedEdge, onEdgeUpdate]);

  const handleColorPreset = useCallback((color: string) => {
    handleUpdate('color', color);
  }, [handleUpdate]);

  if (!isVisible || !selectedEdge) {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      top: '80px',
      left: '20px',
      width: '320px',
      maxHeight: 'calc(100vh - 100px)',
      overflowY: 'auto',
      zIndex: 1000
    }}>
      <GlassPanel variant="elevated" padding="4" style={{
        borderRadius: MermaidDesignTokens.borderRadius.xl,
        boxShadow: MermaidDesignTokens.shadows.glass.xl
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: MermaidDesignTokens.spacing[4],
          paddingBottom: MermaidDesignTokens.spacing[3],
          borderBottom: `1px solid ${MermaidDesignTokens.colors.glass.border}`
        }}>
          <h3 style={{
            fontSize: MermaidDesignTokens.typography.fontSize.lg,
            fontWeight: MermaidDesignTokens.typography.fontWeight.semibold,
            color: MermaidDesignTokens.colors.text.primary,
            margin: 0
          }}>
            Connection Properties
          </h3>
          <GlassButton
            variant="ghost"
            size="sm"
            icon={<Minus size={16} />}
            onClick={onClose}
          />
        </div>

        {/* Label */}
        <div style={{ marginBottom: MermaidDesignTokens.spacing[4] }}>
          <label style={{
            display: 'block',
            fontSize: MermaidDesignTokens.typography.fontSize.sm,
            fontWeight: MermaidDesignTokens.typography.fontWeight.medium,
            color: MermaidDesignTokens.colors.text.primary,
            marginBottom: MermaidDesignTokens.spacing[2]
          }}>
            Connection Label
          </label>
          <GlassInput
            value={localLabel}
            onChange={(e) => handleUpdate('label', e.target.value)}
            placeholder="Enter connection label..."
            icon={<Type size={16} />}
          />
        </div>

        {/* Arrow Type */}
        <div style={{ marginBottom: MermaidDesignTokens.spacing[4] }}>
          <label style={{
            display: 'block',
            fontSize: MermaidDesignTokens.typography.fontSize.sm,
            fontWeight: MermaidDesignTokens.typography.fontWeight.medium,
            color: MermaidDesignTokens.colors.text.primary,
            marginBottom: MermaidDesignTokens.spacing[2]
          }}>
            Arrow Type
          </label>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(2, 1fr)',
            gap: MermaidDesignTokens.spacing[2]
          }}>
            {ARROW_TYPES.map((arrow) => {
              const Icon = arrow.icon;
              return (
                <GlassButton
                  key={arrow.value}
                  variant={localArrowType === arrow.value ? 'primary' : 'ghost'}
                  size="sm"
                  icon={<Icon size={16} />}
                  onClick={() => handleUpdate('arrowType', arrow.value)}
                  style={{ justifyContent: 'center', fontSize: MermaidDesignTokens.typography.fontSize.xs }}
                >
                  {arrow.label}
                </GlassButton>
              );
            })}
          </div>
        </div>

        {/* Line Style */}
        <div style={{ marginBottom: MermaidDesignTokens.spacing[4] }}>
          <label style={{
            display: 'block',
            fontSize: MermaidDesignTokens.typography.fontSize.sm,
            fontWeight: MermaidDesignTokens.typography.fontWeight.medium,
            color: MermaidDesignTokens.colors.text.primary,
            marginBottom: MermaidDesignTokens.spacing[2]
          }}>
            Line Style
          </label>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 1fr)',
            gap: MermaidDesignTokens.spacing[2]
          }}>
            {LINE_STYLES.map((style) => (
              <GlassButton
                key={style.value}
                variant={localLineStyle === style.value ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => handleUpdate('lineStyle', style.value)}
                style={{ justifyContent: 'center', fontSize: MermaidDesignTokens.typography.fontSize.xs }}
              >
                {style.label}
              </GlassButton>
            ))}
          </div>
        </div>

        {/* Color Presets */}
        <div style={{ marginBottom: MermaidDesignTokens.spacing[4] }}>
          <label style={{
            display: 'block',
            fontSize: MermaidDesignTokens.typography.fontSize.sm,
            fontWeight: MermaidDesignTokens.typography.fontWeight.medium,
            color: MermaidDesignTokens.colors.text.primary,
            marginBottom: MermaidDesignTokens.spacing[2]
          }}>
            Line Color
          </label>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(6, 1fr)',
            gap: MermaidDesignTokens.spacing[2],
            marginBottom: MermaidDesignTokens.spacing[3]
          }}>
            {COLOR_PRESETS.map((preset) => (
              <button
                key={preset.name}
                onClick={() => handleColorPreset(preset.color)}
                style={{
                  width: '32px',
                  height: '32px',
                  borderRadius: MermaidDesignTokens.borderRadius.md,
                  background: preset.color,
                  border: localColor === preset.color ? `3px solid ${MermaidDesignTokens.colors.accent.blue}` : '1px solid #e2e8f0',
                  cursor: 'pointer',
                  transition: 'transform 0.2s ease'
                }}
                title={preset.name}
                onMouseEnter={(e) => e.currentTarget.style.transform = 'scale(1.1)'}
                onMouseLeave={(e) => e.currentTarget.style.transform = 'scale(1)'}
              />
            ))}
          </div>
          
          {/* Custom Color */}
          <input
            type="color"
            value={localColor}
            onChange={(e) => handleUpdate('color', e.target.value)}
            style={{
              width: '100%',
              height: '40px',
              border: 'none',
              borderRadius: MermaidDesignTokens.borderRadius.md,
              cursor: 'pointer'
            }}
          />
        </div>

        {/* Line Width */}
        <div style={{ marginBottom: MermaidDesignTokens.spacing[4] }}>
          <label style={{
            display: 'block',
            fontSize: MermaidDesignTokens.typography.fontSize.sm,
            fontWeight: MermaidDesignTokens.typography.fontWeight.medium,
            color: MermaidDesignTokens.colors.text.primary,
            marginBottom: MermaidDesignTokens.spacing[2]
          }}>
            Line Width: {localWidth}px
          </label>
          <input
            type="range"
            min="1"
            max="8"
            value={localWidth}
            onChange={(e) => handleUpdate('width', parseInt(e.target.value))}
            style={{
              width: '100%',
              height: '6px',
              borderRadius: '3px',
              background: MermaidDesignTokens.colors.glass.border,
              outline: 'none',
              cursor: 'pointer'
            }}
          />
        </div>

        {/* Lock/Unlock */}
        <div style={{ marginBottom: MermaidDesignTokens.spacing[4] }}>
          <GlassButton
            variant={isLocked ? 'primary' : 'ghost'}
            size="sm"
            icon={isLocked ? <Lock size={16} /> : <Unlock size={16} />}
            onClick={() => handleUpdate('locked', !isLocked)}
            style={{ width: '100%' }}
          >
            {isLocked ? 'Locked Position' : 'Unlock Position'}
          </GlassButton>
        </div>

        {/* Actions */}
        <div style={{
          display: 'flex',
          gap: MermaidDesignTokens.spacing[2],
          paddingTop: MermaidDesignTokens.spacing[3],
          borderTop: `1px solid ${MermaidDesignTokens.colors.glass.border}`
        }}>
          <GlassButton
            variant="ghost"
            size="sm"
            icon={<Copy size={16} />}
            onClick={() => onEdgeDuplicate(selectedEdge.id)}
            style={{ flex: 1 }}
          >
            Duplicate
          </GlassButton>
          <GlassButton
            variant="ghost"
            size="sm"
            icon={<Trash2 size={16} />}
            onClick={() => onEdgeDelete(selectedEdge.id)}
            style={{ flex: 1, color: MermaidDesignTokens.colors.semantic.error[500] }}
          >
            Delete
          </GlassButton>
        </div>
      </GlassPanel>
    </div>
  );
};

export default EdgePropertiesPanel;

/**
 * Mermaid Visual Editor - Following Design Inspiration
 * Clean white & blue theme with left template panel
 * No visible code - pure visual interface
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  ArrowLeft, Download, Settings, Palette,
  Undo, Redo, ZoomIn, ZoomOut,
  Brain, Sparkles, Plus, Search, X, Edit3, Eye
} from 'lucide-react';
import {
  GlassPanel,
  GlassButton,
  GlassInput,
  AnimationProvider,
  FadeInContainer,
  MermaidDesignTokens
} from '../ui';
import { MermaidPreviewPane } from './MermaidPreviewPane';
import { InteractiveMermaidEditor } from './InteractiveMermaidEditor';
import { MermaidAIService } from '../../services/ai/MermaidAIService';
import { PREMIUM_TEMPLATES, getTemplatesByCategory, getPopularTemplates, searchTemplates } from '../../data/premiumTemplates';

interface MermaidVisualEditorProps {
  designId?: string;
  showBackButton?: boolean;
  onBack?: () => void;
}

// Template categories from premium library
const TEMPLATE_CATEGORIES = [
  { id: 'all', name: 'All Templates', count: PREMIUM_TEMPLATES.length },
  { id: 'security', name: 'Security & Risk', count: getTemplatesByCategory('security').length },
  { id: 'compliance', name: 'Compliance & Audit', count: getTemplatesByCategory('compliance').length },
  { id: 'business', name: 'Business Process', count: getTemplatesByCategory('business').length },
  { id: 'technical', name: 'Technical Architecture', count: getTemplatesByCategory('technical').length },
  { id: 'project', name: 'Project Management', count: getTemplatesByCategory('project').length }
];

// Using premium template library

export const MermaidVisualEditor: React.FC<MermaidVisualEditorProps> = ({
  designId = 'visual-mermaid-editor',
  showBackButton = true,
  onBack
}) => {
  // State Management
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  // Remove viewMode - only interactive mode
  const [diagramText, setDiagramText] = useState(`flowchart TD
    A[Start Your Secure Design Journey] --> B[Select Template]
    B --> C[Customize Design]
    C --> D[Export & Share]

    style A fill:#2563eb,stroke:#1d4ed8,stroke-width:2px,color:#fff
    style B fill:#3b82f6,stroke:#2563eb,stroke-width:2px,color:#fff
    style C fill:#60a5fa,stroke:#3b82f6,stroke-width:2px,color:#fff
    style D fill:#93c5fd,stroke:#60a5fa,stroke-width:2px,color:#fff`);
  const [zoom, setZoom] = useState(1);
  const [isRendering, setIsRendering] = useState(false);

  // Filter templates based on category and search using premium library
  const filteredTemplates = React.useMemo(() => {
    let templates = PREMIUM_TEMPLATES;

    // Filter by category
    if (selectedCategory !== 'all') {
      templates = getTemplatesByCategory(selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      templates = searchTemplates(searchQuery);
      // If we have a category filter, apply it to search results
      if (selectedCategory !== 'all') {
        templates = templates.filter(t => t.category === selectedCategory);
      }
    }

    return templates;
  }, [selectedCategory, searchQuery]);

  // Handle template selection
  const handleTemplateSelect = useCallback((template: typeof PREMIUM_TEMPLATES[0]) => {
    setSelectedTemplate(template.id);
    setDiagramText(template.mermaidCode);
    setIsRendering(true);
    setTimeout(() => setIsRendering(false), 500);
  }, []);

  // Handle AI generation
  const handleAIGenerate = useCallback(async (prompt: string) => {
    setIsRendering(true);
    try {
      const aiService = new MermaidAIService();
      const generatedCode = await aiService.generateDiagram(prompt);
      setDiagramText(generatedCode);
    } catch (error) {
      console.error('AI generation failed:', error);
    } finally {
      setIsRendering(false);
    }
  }, []);

  // Handle export
  const handleExport = useCallback((format: string) => {
    console.log(`Exporting diagram as ${format}`);
  }, []);

  return (
    <AnimationProvider>
      <div style={{
        height: '100vh',
        width: '100vw',
        background: MermaidDesignTokens.colors.primary.gradient,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        {/* Header */}
        <GlassPanel variant="elevated" padding="4" style={{
          borderRadius: 0,
          borderBottom: `1px solid ${MermaidDesignTokens.colors.glass.border}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          minHeight: '64px'
        }}>
          {/* Left side */}
          <div style={{ display: 'flex', alignItems: 'center', gap: MermaidDesignTokens.spacing[4] }}>
            {showBackButton && (
              <GlassButton
                variant="ghost"
                size="sm"
                icon={<ArrowLeft size={16} />}
                onClick={onBack}
              >
                Back
              </GlassButton>
            )}

            <div>
              <h1 style={{
                fontSize: MermaidDesignTokens.typography.fontSize.xl,
                fontWeight: MermaidDesignTokens.typography.fontWeight.bold,
                color: MermaidDesignTokens.colors.text.primary,
                margin: 0
              }}>
                AI Security Architect
              </h1>
              <p style={{
                fontSize: MermaidDesignTokens.typography.fontSize.sm,
                color: MermaidDesignTokens.colors.text.secondary,
                margin: 0
              }}>
                Leverage AI to visualize your security posture
              </p>
            </div>
          </div>

          {/* Center - AI Prompt */}
          <div style={{ display: 'flex', alignItems: 'center', gap: MermaidDesignTokens.spacing[2] }}>
            <GlassInput
              placeholder="Describe your diagram..."
              style={{ width: '300px' }}
              icon={<Brain size={16} />}
            />
            <GlassButton
              variant="primary"
              size="sm"
              icon={<Sparkles size={16} />}
            >
              Generate
            </GlassButton>
          </div>

          {/* Right side */}
          <div style={{ display: 'flex', alignItems: 'center', gap: MermaidDesignTokens.spacing[2] }}>
            <GlassButton variant="ghost" size="sm" icon={<Settings size={16} />} />
            <GlassButton variant="ghost" size="sm" icon={<Download size={16} />} onClick={() => handleExport('png')} />
          </div>
        </GlassPanel>

        {/* Main Content */}
        <div style={{ flex: 1, display: 'flex', overflow: 'hidden' }}>
          {/* Left Template Panel */}
          <div style={{
            width: '400px',
            background: MermaidDesignTokens.colors.glass.primary,
            borderRight: `1px solid ${MermaidDesignTokens.colors.glass.border}`,
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden'
          }}>
            {/* Template Panel Header */}
            <div style={{
              padding: MermaidDesignTokens.spacing[4],
              borderBottom: `1px solid ${MermaidDesignTokens.colors.glass.border}`
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: MermaidDesignTokens.spacing[3]
              }}>
                <h2 style={{
                  fontSize: MermaidDesignTokens.typography.fontSize.lg,
                  fontWeight: MermaidDesignTokens.typography.fontWeight.semibold,
                  color: MermaidDesignTokens.colors.text.primary,
                  margin: 0
                }}>
                  Process Flow Templates
                </h2>
                <GlassButton variant="ghost" size="sm" icon={<X size={16} />} />
              </div>

              <GlassInput
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                icon={<Search size={16} />}
                style={{ marginBottom: MermaidDesignTokens.spacing[3] }}
              />
            </div>

            {/* Template Content */}
            <div style={{ flex: 1, overflow: 'auto', padding: MermaidDesignTokens.spacing[4] }}>
              {/* Premium Template Grid */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(2, 1fr)',
                gap: MermaidDesignTokens.spacing[3]
              }}>
                {filteredTemplates.map((template) => (
                  <GlassPanel
                    key={template.id}
                    variant="secondary"
                    padding="3"
                    style={{
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      border: selectedTemplate === template.id
                        ? `2px solid ${MermaidDesignTokens.colors.accent.blue}`
                        : `1px solid ${MermaidDesignTokens.colors.glass.border}`,
                      position: 'relative'
                    }}
                    onClick={() => handleTemplateSelect(template)}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = MermaidDesignTokens.shadows.glass.lg;
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  >
                    {/* Premium Badge */}
                    {template.isPremium && (
                      <div style={{
                        position: 'absolute',
                        top: '8px',
                        right: '8px',
                        background: 'linear-gradient(135deg, #ffd700 0%, #ffb300 100%)',
                        color: '#1a1a1a',
                        fontSize: MermaidDesignTokens.typography.fontSize.xs,
                        fontWeight: MermaidDesignTokens.typography.fontWeight.bold,
                        padding: '2px 6px',
                        borderRadius: MermaidDesignTokens.borderRadius.base,
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                      }}>
                        AuditReady
                      </div>
                    )}

                    <div style={{
                      fontSize: '2rem',
                      textAlign: 'center',
                      marginBottom: MermaidDesignTokens.spacing[2]
                    }}>
                      {template.thumbnail}
                    </div>

                    <h3 style={{
                      fontSize: MermaidDesignTokens.typography.fontSize.sm,
                      fontWeight: MermaidDesignTokens.typography.fontWeight.semibold,
                      color: MermaidDesignTokens.colors.text.primary,
                      margin: `0 0 ${MermaidDesignTokens.spacing[1]} 0`,
                      textAlign: 'center',
                      lineHeight: MermaidDesignTokens.typography.lineHeight.tight
                    }}>
                      {template.name}
                    </h3>

                    <p style={{
                      fontSize: MermaidDesignTokens.typography.fontSize.xs,
                      color: MermaidDesignTokens.colors.text.secondary,
                      margin: `0 0 ${MermaidDesignTokens.spacing[2]} 0`,
                      textAlign: 'center',
                      lineHeight: MermaidDesignTokens.typography.lineHeight.tight
                    }}>
                      {template.description}
                    </p>

                    {/* Template Stats */}
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      fontSize: MermaidDesignTokens.typography.fontSize.xs,
                      color: MermaidDesignTokens.colors.text.tertiary
                    }}>
                      {template.rating && (
                        <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
                          <span>⭐</span>
                          <span>{template.rating}</span>
                        </div>
                      )}
                      {template.usageCount && (
                        <div>
                          {template.usageCount.toLocaleString()} uses
                        </div>
                      )}
                    </div>

                    {/* Complexity Badge */}
                    <div style={{
                      marginTop: MermaidDesignTokens.spacing[2],
                      textAlign: 'center'
                    }}>
                      <span style={{
                        fontSize: MermaidDesignTokens.typography.fontSize.xs,
                        color: template.complexity === 'simple' ? '#10b981' :
                               template.complexity === 'medium' ? '#f59e0b' : '#ef4444',
                        background: template.complexity === 'simple' ? '#ecfdf5' :
                                   template.complexity === 'medium' ? '#fffbeb' : '#fef2f2',
                        padding: '2px 6px',
                        borderRadius: MermaidDesignTokens.borderRadius.base,
                        border: `1px solid ${template.complexity === 'simple' ? '#10b981' :
                                             template.complexity === 'medium' ? '#f59e0b' : '#ef4444'}`
                      }}>
                        {template.complexity}
                      </span>
                    </div>
                  </GlassPanel>
                ))}
              </div>

              {/* Start Blank Button */}
              <div style={{ marginTop: MermaidDesignTokens.spacing[4] }}>
                <GlassButton
                  variant="primary"
                  size="base"
                  icon={<Plus size={16} />}
                  style={{ width: '100%' }}
                  onClick={() => {
                    setSelectedTemplate(null);
                    setDiagramText('flowchart TD\n    A[Start] --> B[End]');
                  }}
                >
                  Start Blank Diagram
                </GlassButton>
              </div>
            </div>
          </div>

          {/* Main Canvas Area - Interactive Only */}
          <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
            <InteractiveMermaidEditor
              onMermaidCodeChange={(code) => {
                setDiagramText(code);
                setIsRendering(true);
                setTimeout(() => setIsRendering(false), 300);
              }}
            />
          </div>
        </div>
      </div>
    </AnimationProvider>
  );
};

export default MermaidVisualEditor;

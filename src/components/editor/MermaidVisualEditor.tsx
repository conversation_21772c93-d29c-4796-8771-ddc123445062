/**
 * Mermaid Visual Editor - Following Design Inspiration
 * Clean white & blue theme with left template panel
 * No visible code - pure visual interface
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  ArrowLeft, Download, Settings, Palette,
  Undo, Redo, ZoomIn, ZoomOut,
  Brain, Sparkles, Plus, Search, X, Edit3, Eye
} from 'lucide-react';
import {
  GlassPanel,
  GlassButton,
  GlassInput,
  AnimationProvider,
  FadeInContainer,
  MermaidDesignTokens
} from '../ui';
import { MermaidPreviewPane } from './MermaidPreviewPane';
import { InteractiveMermaidEditor } from './InteractiveMermaidEditor';
import { MermaidAIService } from '../../services/ai/MermaidAIService';

interface MermaidVisualEditorProps {
  designId?: string;
  showBackButton?: boolean;
  onBack?: () => void;
}

// Template categories following the design inspiration
const TEMPLATE_CATEGORIES = [
  { id: 'all', name: 'All Templates', count: 24 },
  { id: 'security', name: 'Security & Compliance', count: 8 },
  { id: 'process', name: 'Business Process', count: 6 },
  { id: 'data', name: 'Data Flow', count: 4 },
  { id: 'organization', name: 'Organization', count: 3 },
  { id: 'project', name: 'Project Management', count: 3 }
];

// Template library following the design inspiration
const PROCESS_TEMPLATES = [
  {
    id: 'threat-modeling',
    name: 'Threat Modeling Process',
    description: 'Identify threats and vulnerabilities in your system.',
    category: 'security',
    thumbnail: '🛡️',
    code: `flowchart TD
    A[Asset Identification] --> B[Threat Identification]
    B --> C[Vulnerability Assessment]
    C --> D[Risk Analysis]
    D --> E[Mitigation Strategies]
    E --> F[Implementation]
    F --> G[Monitoring & Review]`
  },
  {
    id: 'incident-response',
    name: 'Incident Response Plan',
    description: 'Steps to handle security breaches effectively.',
    category: 'security',
    thumbnail: '🚨',
    code: `flowchart TD
    A[Incident Detection] --> B[Initial Assessment]
    B --> C[Containment]
    C --> D[Investigation]
    D --> E[Eradication]
    E --> F[Recovery]
    F --> G[Lessons Learned]`
  },
  {
    id: 'devops-pipeline',
    name: 'DevSecOps Pipeline',
    description: 'Integrate security into your development lifecycle.',
    category: 'process',
    thumbnail: '🔄',
    code: `flowchart LR
    A[Code] --> B[Build]
    B --> C[Security Scan]
    C --> D[Test]
    D --> E[Deploy]
    E --> F[Monitor]
    F --> A`
  },
  {
    id: 'vulnerability-mgmt',
    name: 'Vulnerability Management',
    description: 'Process for identifying and remediating vulnerabilities.',
    category: 'security',
    thumbnail: '🔍',
    code: `flowchart TD
    A[Asset Discovery] --> B[Vulnerability Scanning]
    B --> C[Risk Assessment]
    C --> D[Prioritization]
    D --> E[Remediation]
    E --> F[Verification]
    F --> A`
  },
  {
    id: 'security-training',
    name: 'Security Awareness Training',
    description: 'Flow for educating employees on security best practices.',
    category: 'security',
    thumbnail: '🎓',
    code: `flowchart TD
    A[Training Needs Assessment] --> B[Content Development]
    B --> C[Training Delivery]
    C --> D[Knowledge Assessment]
    D --> E[Feedback Collection]
    E --> F[Program Improvement]
    F --> A`
  },
  {
    id: 'cloud-security',
    name: 'Cloud Security Review',
    description: 'Process for assessing cloud environment security.',
    category: 'security',
    thumbnail: '☁️',
    code: `flowchart TD
    A[Cloud Asset Inventory] --> B[Configuration Review]
    B --> C[Access Control Audit]
    C --> D[Data Protection Check]
    D --> E[Compliance Validation]
    E --> F[Risk Mitigation]
    F --> A`
  },
  {
    id: 'access-control',
    name: 'Access Control Workflow',
    description: 'Managing user permissions and access rights.',
    category: 'security',
    thumbnail: '🔐',
    code: `flowchart TD
    A[Access Request] --> B[Manager Approval]
    B --> C[Security Review]
    C --> D[Provisioning]
    D --> E[Access Granted]
    E --> F[Periodic Review]
    F --> G[Access Revocation]`
  },
  {
    id: 'data-privacy',
    name: 'Data Privacy Compliance',
    description: 'Ensure adherence to data protection regulations.',
    category: 'security',
    thumbnail: '🛡️',
    code: `flowchart TD
    A[Data Mapping] --> B[Privacy Impact Assessment]
    B --> C[Consent Management]
    C --> D[Data Processing]
    D --> E[Subject Rights Handling]
    E --> F[Breach Response]
    F --> A`
  }
];

export const MermaidVisualEditor: React.FC<MermaidVisualEditorProps> = ({
  designId = 'visual-mermaid-editor',
  showBackButton = true,
  onBack
}) => {
  // State Management
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'interactive' | 'preview'>('interactive');
  const [diagramText, setDiagramText] = useState(`flowchart TD
    A[Start Your Secure Design Journey] --> B[Select Template]
    B --> C[Customize Design]
    C --> D[Export & Share]

    style A fill:#2563eb,stroke:#1d4ed8,stroke-width:2px,color:#fff
    style B fill:#3b82f6,stroke:#2563eb,stroke-width:2px,color:#fff
    style C fill:#60a5fa,stroke:#3b82f6,stroke-width:2px,color:#fff
    style D fill:#93c5fd,stroke:#60a5fa,stroke-width:2px,color:#fff`);
  const [zoom, setZoom] = useState(1);
  const [isRendering, setIsRendering] = useState(false);

  // Filter templates based on category and search
  const filteredTemplates = PROCESS_TEMPLATES.filter(template => {
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  // Handle template selection
  const handleTemplateSelect = useCallback((template: typeof PROCESS_TEMPLATES[0]) => {
    setSelectedTemplate(template.id);
    setDiagramText(template.code);
    setIsRendering(true);
    setTimeout(() => setIsRendering(false), 500);
  }, []);

  // Handle AI generation
  const handleAIGenerate = useCallback(async (prompt: string) => {
    setIsRendering(true);
    try {
      const aiService = new MermaidAIService();
      const generatedCode = await aiService.generateDiagram(prompt);
      setDiagramText(generatedCode);
    } catch (error) {
      console.error('AI generation failed:', error);
    } finally {
      setIsRendering(false);
    }
  }, []);

  // Handle export
  const handleExport = useCallback((format: string) => {
    console.log(`Exporting diagram as ${format}`);
  }, []);

  return (
    <AnimationProvider>
      <div style={{
        height: '100vh',
        width: '100vw',
        background: MermaidDesignTokens.colors.primary.gradient,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        {/* Header */}
        <GlassPanel variant="elevated" padding="4" style={{
          borderRadius: 0,
          borderBottom: `1px solid ${MermaidDesignTokens.colors.glass.border}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          minHeight: '64px'
        }}>
          {/* Left side */}
          <div style={{ display: 'flex', alignItems: 'center', gap: MermaidDesignTokens.spacing[4] }}>
            {showBackButton && (
              <GlassButton
                variant="ghost"
                size="sm"
                icon={<ArrowLeft size={16} />}
                onClick={onBack}
              >
                Back
              </GlassButton>
            )}

            <div>
              <h1 style={{
                fontSize: MermaidDesignTokens.typography.fontSize.xl,
                fontWeight: MermaidDesignTokens.typography.fontWeight.bold,
                color: MermaidDesignTokens.colors.text.primary,
                margin: 0
              }}>
                AI Security Architect
              </h1>
              <p style={{
                fontSize: MermaidDesignTokens.typography.fontSize.sm,
                color: MermaidDesignTokens.colors.text.secondary,
                margin: 0
              }}>
                Leverage AI to visualize your security posture
              </p>
            </div>
          </div>

          {/* Center - View Mode Toggle */}
          <div style={{ display: 'flex', alignItems: 'center', gap: MermaidDesignTokens.spacing[1] }}>
            <GlassButton
              variant={viewMode === 'interactive' ? 'primary' : 'ghost'}
              size="sm"
              icon={<Edit3 size={16} />}
              onClick={() => setViewMode('interactive')}
            >
              Interactive
            </GlassButton>
            <GlassButton
              variant={viewMode === 'preview' ? 'primary' : 'ghost'}
              size="sm"
              icon={<Eye size={16} />}
              onClick={() => setViewMode('preview')}
            >
              Preview
            </GlassButton>
          </div>

          {/* Right side */}
          <div style={{ display: 'flex', alignItems: 'center', gap: MermaidDesignTokens.spacing[2] }}>
            <GlassButton variant="ghost" size="sm" icon={<Settings size={16} />} />
            <GlassButton variant="ghost" size="sm" icon={<Download size={16} />} onClick={() => handleExport('png')} />
          </div>
        </GlassPanel>

        {/* Main Content */}
        <div style={{ flex: 1, display: 'flex', overflow: 'hidden' }}>
          {/* Left Template Panel */}
          <div style={{
            width: '400px',
            background: MermaidDesignTokens.colors.glass.primary,
            borderRight: `1px solid ${MermaidDesignTokens.colors.glass.border}`,
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden'
          }}>
            {/* Template Panel Header */}
            <div style={{
              padding: MermaidDesignTokens.spacing[4],
              borderBottom: `1px solid ${MermaidDesignTokens.colors.glass.border}`
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: MermaidDesignTokens.spacing[3]
              }}>
                <h2 style={{
                  fontSize: MermaidDesignTokens.typography.fontSize.lg,
                  fontWeight: MermaidDesignTokens.typography.fontWeight.semibold,
                  color: MermaidDesignTokens.colors.text.primary,
                  margin: 0
                }}>
                  Process Flow Templates
                </h2>
                <GlassButton variant="ghost" size="sm" icon={<X size={16} />} />
              </div>

              <GlassInput
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                icon={<Search size={16} />}
                style={{ marginBottom: MermaidDesignTokens.spacing[3] }}
              />
            </div>

            {/* Template Content */}
            <div style={{ flex: 1, overflow: 'auto', padding: MermaidDesignTokens.spacing[4] }}>
              {/* Template Grid */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(2, 1fr)',
                gap: MermaidDesignTokens.spacing[3]
              }}>
                {filteredTemplates.map((template) => (
                  <GlassPanel
                    key={template.id}
                    variant="secondary"
                    padding="3"
                    style={{
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      border: selectedTemplate === template.id
                        ? `2px solid ${MermaidDesignTokens.colors.accent.blue}`
                        : `1px solid ${MermaidDesignTokens.colors.glass.border}`,
                      ':hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: MermaidDesignTokens.shadows.glass.lg
                      }
                    }}
                    onClick={() => handleTemplateSelect(template)}
                  >
                    <div style={{
                      fontSize: '2rem',
                      textAlign: 'center',
                      marginBottom: MermaidDesignTokens.spacing[2]
                    }}>
                      {template.thumbnail}
                    </div>
                    <h3 style={{
                      fontSize: MermaidDesignTokens.typography.fontSize.sm,
                      fontWeight: MermaidDesignTokens.typography.fontWeight.semibold,
                      color: MermaidDesignTokens.colors.text.primary,
                      margin: `0 0 ${MermaidDesignTokens.spacing[1]} 0`,
                      textAlign: 'center'
                    }}>
                      {template.name}
                    </h3>
                    <p style={{
                      fontSize: MermaidDesignTokens.typography.fontSize.xs,
                      color: MermaidDesignTokens.colors.text.secondary,
                      margin: 0,
                      textAlign: 'center',
                      lineHeight: MermaidDesignTokens.typography.lineHeight.tight
                    }}>
                      {template.description}
                    </p>
                  </GlassPanel>
                ))}
              </div>

              {/* Start Blank Button */}
              <div style={{ marginTop: MermaidDesignTokens.spacing[4] }}>
                <GlassButton
                  variant="primary"
                  size="base"
                  icon={<Plus size={16} />}
                  style={{ width: '100%' }}
                  onClick={() => {
                    setSelectedTemplate(null);
                    setDiagramText('flowchart TD\n    A[Start] --> B[End]');
                  }}
                >
                  Start Blank Diagram
                </GlassButton>
              </div>
            </div>
          </div>

          {/* Main Canvas Area */}
          <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
            {viewMode === 'interactive' ? (
              <InteractiveMermaidEditor
                onMermaidCodeChange={(code) => {
                  setDiagramText(code);
                  setIsRendering(true);
                  setTimeout(() => setIsRendering(false), 300);
                }}
              />
            ) : (
              <MermaidPreviewPane
                diagramText={diagramText}
                isRendering={isRendering}
                onExport={handleExport}
                zoom={zoom}
                onZoomChange={setZoom}
              />
            )}
          </div>
        </div>
      </div>
    </AnimationProvider>
  );
};

export default MermaidVisualEditor;

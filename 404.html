<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="description" content="AuditReady - Modern Compliance Management Platform" />
    <meta name="author" content="AuditReady" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    
    <!-- Open Graph / Social Media Meta Tags -->
    <meta property="og:title" content="AuditReady - Security Compliance Platform" />
    <meta property="og:description" content="Transform your compliance journey with AuditReady - the intelligent platform designed for modern security teams." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://seriouz85.github.io/" />
    <meta property="og:image" content="https://seriouz85.github.io/og-image.svg" />
    
    <title>AuditReady</title>

    <!-- D3 and D3-org-chart -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/d3-org-chart@3.1.1/build/d3-org-chart.min.js"></script>
    
    <!-- SPA Routing for GitHub Pages -->
    <script type="text/javascript">
      // Only run this script on GitHub Pages
      if (window.location.hostname.includes('github.io')) {
        // Single Page Apps for GitHub Pages
        // MIT License
        // https://github.com/rafgraph/spa-github-pages
        (function(l) {
          if (l.search[1] === '/' ) {
            var decoded = l.search.slice(1).split('&').map(function(s) { 
              return s.replace(/~and~/g, '&')
            }).join('?');
            window.history.replaceState(null, null,
                l.pathname.slice(0, -1) + decoded + l.hash
            );
          }
        }(window.location))
      }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html> 
{"name": "audit-readiness-hub", "private": true, "version": "1.0.0", "type": "module", "homepage": "https://seriouz85.github.io/audit-readiness-hub/", "scripts": {"dev": "vite", "build": "vite build", "build:github": "GITHUB_PAGES=true vite build --mode github", "build:vercel": "vite build --mode vercel", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "predeploy": "npm run build:github", "deploy": "gh-pages -d dist -b gh-pages", "test:mermaid": "jest src/services/mermaid", "build:mermaid": "tsc --project tsconfig.mermaid.json", "dev:mermaid": "npm run test:mermaid -- --watch"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@chatscope/chat-ui-kit-react": "^2.0.3", "@chatscope/chat-ui-kit-styles": "^1.4.0", "@emotion/is-prop-valid": "^1.3.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@firebase/firestore": "^4.7.10", "@hookform/resolvers": "^3.9.0", "@monaco-editor/react": "^4.7.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@reactflow/node-resizer": "^2.2.14", "@tanstack/react-query": "^5.56.2", "@tremor/react": "^3.18.7", "@types/echarts": "^5.0.0", "@types/fabric": "^5.3.10", "@types/jspdf": "^2.0.0", "@types/react-router-dom": "^5.3.3", "@xyflow/react": "^12.6.4", "antd": "^5.24.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "d3": "^7.9.0", "d3-flextree": "^2.1.2", "d3-hierarchy": "^3.1.2", "d3-org-chart": "^3.1.1", "dagre": "^0.8.5", "date-fns": "^3.6.0", "docx": "^9.5.0", "echarts": "^5.6.0", "embla-carousel-react": "^8.0.0-rc11", "fabric": "^6.6.5", "file-saver": "^2.0.5", "firebase": "^11.6.0", "framer-motion": "^12.9.4", "html2canvas": "^1.4.1", "i18next": "^25.0.0", "input-otp": "^1.2.4", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lodash": "^4.17.21", "lucide-react": "^0.462.0", "marked": "^15.0.11", "mermaid": "^10.9.3", "monaco-editor": "^0.52.2", "next-auth": "^4.24.11", "next-themes": "^0.3.0", "primeicons": "^7.0.0", "primereact": "^10.9.4", "qrcode.react": "^3.1.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.4.1", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.22.1", "react-to-print": "^3.0.6", "reactflow": "^11.11.4", "recharts": "^2.12.7", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "sonner": "^1.5.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/d3": "^7.4.3", "@types/dagre": "^0.7.52", "@types/estree": "^1.0.1", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.17", "@types/node": "^22.14.1", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "@typescript-eslint/types": "^8.30.1", "@vitejs/plugin-react": "^4.4.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "gh-pages": "^6.3.0", "globals": "^15.9.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "ts-jest": "^29.3.4", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^6.3.5"}}
name: Deploy to GitHub Pages

on:
  push:
    branches:
      - main

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: write

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install Dependencies
        run: npm ci
        
      - name: Build
        run: npm run build
      
      - name: Copy build files to repository root
        run: |
          cp -r dist/* .
          cp dist/index.html dist/404.html
          touch .nojekyll
      
      - name: Commit and Push
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add -A
          git commit -m "Deploy website updates" || echo "No changes to commit"
          git push 